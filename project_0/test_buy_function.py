#!/usr/bin/env python3
"""
测试买入功能的简单脚本
"""

import requests
import json

def test_buy_function():
    """测试买入功能"""
    # 测试数据
    test_data = {
        'code': '000001',  # 平安银行
        'action': '买入',
        'amount': 100
    }
    
    # 发送POST请求
    url = 'http://localhost:5000/api/execute_action'
    headers = {'Content-Type': 'application/json'}
    
    try:
        response = requests.post(url, headers=headers, json=test_data)
        result = response.json()
        
        print("测试结果:")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get('success'):
            print("✅ 买入功能测试成功!")
        else:
            print("❌ 买入功能测试失败!")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")

if __name__ == '__main__':
    test_buy_function()
