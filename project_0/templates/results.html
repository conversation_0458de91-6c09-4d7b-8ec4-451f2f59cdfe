<!doctype html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>打分结果</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .container {
            max-width: 95%;
            margin-top: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #fff;
            border-bottom: none;
            padding: 1.5rem;
        }
        .card-body {
            padding: 2rem;
        }
        .table {
            margin-bottom: 0;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .table td {
            vertical-align: middle;
        }
        .table-hover tbody tr:hover {
            background-color: #f5f5f5;
        }
        .alert {
            border-radius: 10px;
        }
        .btn-back {
            background-color: #6c757d;
            border: none;
            padding: 0.5rem 2rem;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .btn-back:hover {
            background-color: #5a6268;
            color: white;
            transform: translateY(-2px);
        }
        /* 表格容器样式 */
        .table-container {
            max-height: 80vh;  /* 设置最大高度为视口高度的80% */
            overflow-y: auto;  /* 如果内容超出则显示滚动条 */
        }
        /* 表格样式 */
        .table {
            width: 100%;
            margin-bottom: 0;
        }
        /* 表头固定 */
        .table thead th {
            position: sticky;
            top: 0;
            background-color: #f8f9fa;
            z-index: 1;
        }
        /* 数值列的样式 */
        .number-cell {
            text-align: right;
            font-family: 'Consolas', monospace;
        }
        /* 正负值的颜色 */
        .positive { color: #d60000; }
        .negative { color: #009933; }
        /* 排名靠前的行样式 */
        .top-rank {
            background-color: rgba(40, 167, 69, 0.1);
        }
        /* 技术打分列样式 */
        .score-column {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #0d6efd;
        }
        .top-rank .score-column {
            background-color: rgba(40, 167, 69, 0.15);
        }
        /* 鼠标悬停提示样式 */
        [title] {
            position: relative;
            cursor: help;
        }
        [title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            padding: 5px 10px;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 4px;
            font-size: 14px;
            white-space: nowrap;
            z-index: 1000;
        }
        .position-info {
            display: none;
            margin-top: 5px;
            padding: 5px;
            background-color: #f5f5f5;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header text-center">
                <h1 class="mb-0">打分结果</h1>
            </div>
            <div class="card-body">
        {% if error %}
            <div class="alert alert-danger" role="alert">
                        <h4 class="alert-heading">错误</h4>
                        <p>{{ error }}</p>
            </div>
        {% else %}
                    {% if table_data is not none %}
                        <div class="alert alert-info">
                            <h4 class="alert-heading">参数说明</h4>
                            <p>涨跌幅计算周期：{{ RECENT_DAYS_FOR_MOMENTUM }}个交易日</p>
                            <p>相对强度计算周期：{{ RELATIVE_STRENGTH_DAYS }}个交易日，基准指数：{{ BENCHMARK_INDEX }}</p>
                        </div>
                    {% endif %}
                    <p class="text-muted mb-4">以下是根据技术/市场指标打分后的股票排名:</p>
                    <div class="table-container">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>代码</th>
                                    <th>名称</th>
                                    <th class="text-end score-column" title="技术打分：综合各项技术指标的打分结果，分数越高表示综合表现越好">技术打分</th>
                                    <th class="text-end" title="年涨停：过去一年内涨停（涨幅≥9.9%）的天数">年涨停</th>
                                    <th class="text-end" title="年跌停：过去一年内跌停（跌幅≤-9.9%）的天数">年跌停</th>
                                    <th class="text-end" title="gt_5：过去一年内涨幅超过5%但未涨停的天数">gt_5</th>
                                    <th class="text-end" title="lt_5：过去一年内跌幅超过5%但未跌停的天数">lt_5</th>
                                    <th class="text-end" title="{{ RECENT_DAYS_FOR_MOMENTUM }}日涨跌幅：最近{{ RECENT_DAYS_FOR_MOMENTUM }}个交易日的涨跌幅">{{ RECENT_DAYS_FOR_MOMENTUM }}日涨跌幅</th>
                                    <th class="text-end" title="10日成交强度：近10日平均成交额/近30日平均成交额，反映近期成交活跃度">10日成交强度</th>
                                    <th class="text-end" title="{{ RELATIVE_STRENGTH_DAYS }}日相对强度：股票{{ RELATIVE_STRENGTH_DAYS }}日收益率/中小板指{{ RELATIVE_STRENGTH_DAYS }}日收益率，反映相对于大盘的表现">{{ RELATIVE_STRENGTH_DAYS }}日相对强度</th>
                                    <th class="text-end" title="平均振幅：近20日每日振幅的平均值，反映股票的整体波动水平">平均振幅</th>
                                    <th class="text-end" title="显著振幅天数：近20日内振幅超过7%的天数，反映剧烈波动的频率">显著振幅天数</th>
                                    <th class="text-end" title="最大振幅：近20日内的最大振幅，反映最大波动幅度">最大振幅</th>
                                    <th class="text-end" title="跌停惩罚：考虑时间衰减的跌停惩罚分数，分数越高表示风险越大">跌停惩罚</th>
                                    <th class="text-end">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in table_data %}
                                <tr class="{{ 'top-rank' if loop.index <= POSITION_COUNT else '' }}" data-code="{{ row['代码'] }}">
                                    <td>{{ loop.index }}</td>
                                    <td>{{ row['代码'] }}</td>
                                    <td>{{ row['名称'] }}</td>
                                    <td class="number-cell score-column {% if row['技术打分'] >= 90 %}positive{% elif row['技术打分'] <= 60 %}negative{% endif %}">
                                        {{ '%.2f'|format(row['技术打分']) }}
                                    </td>
                                    <td class="number-cell">{{ row['年涨停']|int }}</td>
                                    <td class="number-cell">{{ row['年跌停']|int }}</td>
                                    <td class="number-cell">{{ row['gt_5']|int }}</td>
                                    <td class="number-cell">{{ row['lt_5']|int }}</td>
                                    {% set momentum_col = RECENT_DAYS_FOR_MOMENTUM ~ '日涨跌幅' %}
                                    <td class="number-cell {% if row[momentum_col] > 0 %}positive{% elif row[momentum_col] < 0 %}negative{% endif %}">
                                        {{ '%.2f'|format(row[momentum_col]) }}
                                    </td>
                                    <td class="number-cell {% if row['10日成交强度'] > 1 %}positive{% elif row['10日成交强度'] < 1 %}negative{% endif %}">
                                        {{ '%.2f'|format(row['10日成交强度']) }}
                                    </td>
                                    {% set rs_col = RELATIVE_STRENGTH_DAYS ~ '日相对强度' %}
                                    <td class="number-cell {% if row[rs_col] > 1 %}positive{% elif row[rs_col] < 1 %}negative{% endif %}">
                                        {{ '%.2f'|format(row[rs_col]) }}
                                    </td>
                                    <td class="number-cell {% if row['平均振幅'] is not none and row['平均振幅'] > 5 %}positive{% elif row['平均振幅'] is not none and row['平均振幅'] <= 5 %}negative{% endif %}">
                                        {{ '%.2f'|format(row['平均振幅']) if row['平均振幅'] is not none else '-' }}
                                    </td>
                                    <td class="number-cell">
                                        {{ row['显著振幅天数']|int if row['显著振幅天数'] is not none else '-' }}
                                    </td>
                                    <td class="number-cell {% if row['最大振幅'] is not none and row['最大振幅'] > 7 %}positive{% elif row['最大振幅'] is not none and row['最大振幅'] <= 7 %}negative{% endif %}">
                                        {{ '%.2f'|format(row['最大振幅']) if row['最大振幅'] is not none else '-' }}
                                    </td>
                                    <td class="number-cell {% if row['跌停惩罚'] > 0 %}positive{% elif row['跌停惩罚'] < 0 %}negative{% endif %}">
                                        {{ '%.2f'|format(row['跌停惩罚']) }}
                                    </td>
                                    <td>
                                        {% if row['代码'] in positions %}
                                            <span class="text-success">已持仓</span>
                                            <button onclick="removePosition('{{ row['代码'] }}')" class="btn btn-danger btn-sm">移除持仓</button>
                                        {% else %}
                                            <button onclick="togglePositionForm('{{ row['代码'] }}', '{{ row['名称'] }}')" class="btn">
                                                添加持仓
                                            </button>
                                            <div id="position-form-{{ row['代码'] }}" class="position-info">
                                                <input type="number" id="cost-price-{{ row['代码'] }}" placeholder="成本价" step="0.01" style="width: 80px;">
                                                <input type="number" id="amount-{{ row['代码'] }}" placeholder="数量" style="width: 80px;">
                                                <button onclick="addPosition('{{ row['代码'] }}', '{{ row['名称'] }}')" class="btn">确认</button>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
        {% endif %}

                <div class="text-center mt-4">
                    <a href="{{ url_for('index') }}" class="btn btn-back">返回</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function togglePositionForm(code, name) {
            const form = document.getElementById(`position-form-${code}`);
            form.style.display = form.style.display === 'block' ? 'none' : 'block';
        }

        function addPosition(code, name) {
            const costPrice = document.getElementById(`cost-price-${code}`).value;
            const amount = document.getElementById(`amount-${code}`).value;
            
            if (!costPrice || !amount) {
                alert('请输入成本价和数量');
                return;
            }

            fetch('/position/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    code: code,
                    name: name,
                    cost_price: parseFloat(costPrice),
                    amount: parseInt(amount)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    const row = document.querySelector(`tr[data-code="${code}"]`);
                    if (row) {
                        const actionCell = row.querySelector('td:last-child');
                        actionCell.innerHTML = `
                            <span class="text-success">已持仓</span>
                            <button onclick="removePosition('${code}')" class="btn btn-danger btn-sm">移除持仓</button>
                        `;
                    }
                    const form = document.getElementById(`position-form-${code}`);
                    form.style.display = 'none';
                } else {
                    alert('添加持仓失败: ' + data.message);
                }
            });
        }

        function removePosition(code) {
            if (confirm('确定要移除该持仓吗？')) {
                fetch('/position/remove', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const row = document.querySelector(`tr[data-code="${code}"]`);
                        if (row) {
                            const actionCell = row.querySelector('td:last-child');
                            const name = row.querySelector('td:nth-child(3)').textContent;
                            actionCell.innerHTML = `
                                <button onclick="togglePositionForm('${code}', '${name}')" class="btn">
                                    添加持仓
                                </button>
                                <div id="position-form-${code}" class="position-info">
                                    <input type="number" id="cost-price-${code}" placeholder="成本价" step="0.01" style="width: 80px;">
                                    <input type="number" id="amount-${code}" placeholder="数量" style="width: 80px;">
                                    <button onclick="addPosition('${code}', '${name}')" class="btn">确认</button>
                                </div>
                            `;
                        }
                    } else {
                        alert('移除持仓失败: ' + data.message);
                    }
                });
            }
        }
    </script>
</body>
</html>