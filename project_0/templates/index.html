<!doctype html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>股票技术/市场打分</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #fff;
            border-bottom: none;
            padding: 1.5rem;
        }
        .card-body {
            padding: 2rem;
        }
        .btn-primary {
            background-color: #0d6efd;
            border: none;
            padding: 0.5rem 2rem;
            border-radius: 25px;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
        }
        .form-control-file {
            padding: 0.5rem;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            background-color: #f8f9fa;
        }
        .strategy-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        .strategy-info h4 {
            color: #0d6efd;
            margin-bottom: 1rem;
        }
        .strategy-info ul {
            padding-left: 1.5rem;
        }
        .strategy-info li {
            margin-bottom: 0.5rem;
        }
        .file-info {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .positions {
            margin-bottom: 20px;
        }
        .position-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #fff;
        }
        .position-card.profit {
            border-left: 4px solid #d60000;
        }
        .position-card.loss {
            border-left: 4px solid #009933;
        }
        .upload-form {
            margin-bottom: 20px;
        }
        .btn {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .rebalance-suggestions {
            margin-top: 20px;
        }
        .suggestion-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #fff;
        }
        .suggestion-card.buy {
            border-left: 4px solid #4CAF50;
        }
        .suggestion-card.sell {
            border-left: 4px solid #f44336;
        }
        .positive {
            color: #d60000;
        }
        .negative {
            color: #009933;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header text-center">
                <h1 class="mb-0">股票技术/市场打分</h1>
            </div>
            <div class="card-body">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'success' if category == 'success' else 'danger' if category == 'error' else 'warning' }} alert-dismissible fade show mb-4" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <p class="text-muted mb-4">请上传包含待分析股票列表的 CSV 文件，或直接点击分析按钮使用默认股票列表。</p>

                <!-- 文件信息 -->
                <div class="file-info">
                    <h2>文件信息</h2>
                    {% if file_info and file_info.exists %}
                        <p>最后更新时间: {{ file_info.time }}</p>
                        <p>文件大小: {{ file_info.size }}</p>
                        <form action="{{ url_for('regenerate_stocks') }}" method="post" id="regenerateForm">
                            <button type="submit" class="btn" id="regenerateBtn">
                                <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                重新生成股票列表
                            </button>
        </form>
                        <div class="text-center mt-3">
                            <a href="{{ url_for('positions') }}" class="btn btn-primary">查看持仓</a>
                        </div>
                    {% else %}
                        <p>未找到股票列表文件</p>
                    {% endif %}
                </div>
                
                <!-- 上传表单 -->
                <div class="upload-form">
                    <h2>上传股票列表</h2>
                    <form action="{{ url_for('upload_and_score') }}" method="post" enctype="multipart/form-data" id="analysisForm">
                        <div class="form-group mb-4">
                            <label for="csv_file" class="form-label">选择 CSV 文件（可选）:</label>
                            <input type="file" class="form-control-file" id="csv_file" name="csv_file" accept=".csv">
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary me-2" id="analyzeBtn">
                                <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                开始分析
                            </button>
                        </div>
                    </form>
                </div>

                <div class="strategy-info">
            <h4>策略说明:</h4>
            <ul>
                        <li>默认使用 selected_stocks.csv 中的股票列表进行分析</li>
                        <li>上传的 CSV 文件需包含 '代码' 和 '名称' 两列</li>
                        <li>计算每只股票的技术和市场指标，包括历史涨停次数、涨幅 >5% 次数、近期涨跌幅、日均成交额、相对强度等</li>
                        <li>根据设定的权重对这些指标进行打分</li>
                        <li>按总分降序排列股票</li>
            </ul>
                    <p class="text-muted mt-3">请确保您的 CSV 文件使用 UTF-8 编码</p>
                </div>

                <!-- 调仓建议 -->
                {% if rebalance_suggestions %}
                    <div class="rebalance-suggestions">
                        <h2>调仓建议</h2>
                        {% for suggestion in rebalance_suggestions %}
                            <div class="suggestion-card {% if suggestion.action == '买入' %}buy{% else %}sell{% endif %}">
                                <h3>{{ suggestion.name }} ({{ suggestion.code }})</h3>
                                <p>建议: {{ suggestion.action }}</p>
                                <p>原因: {{ suggestion.reason }}</p>
                                <p>当前得分: {{ suggestion.current_score }}</p>
                                {% if suggestion.action == '买入' %}
                                    <button onclick="addPosition('{{ suggestion.code }}', '{{ suggestion.name }}')" class="btn">添加持仓</button>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="text-center mt-4">
                    <a href="{{ url_for('positions') }}" class="btn btn-primary me-2">查看持仓</a>
                    <a href="{{ url_for('index') }}" class="btn btn-back">返回首页</a>
                </div>

                <script>
                document.getElementById('analysisForm').addEventListener('submit', function(e) {
                    const analyzeBtn = document.getElementById('analyzeBtn');
                    const spinner = analyzeBtn.querySelector('.spinner-border');
                    
                    // 禁用按钮并显示加载动画
                    analyzeBtn.disabled = true;
                    spinner.classList.remove('d-none');
                    analyzeBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 分析中...';
                    
                    // 显示提示信息
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-info alert-dismissible fade show mt-3';
                    alertDiv.innerHTML = `
                        <i class="bi bi-info-circle-fill"></i> 正在分析数据，请稍候...
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    this.parentNode.insertBefore(alertDiv, this.nextSibling);
                });

                function removePosition(code) {
                    if (confirm('确定要移除该持仓吗？')) {
                        fetch('/position/remove', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ code: code })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                location.reload();
                            } else {
                                alert('移除持仓失败: ' + data.message);
                            }
                        });
                    }
                }
                
                function addPosition(code, name) {
                    const costPrice = prompt('请输入买入成本价:');
                    const amount = prompt('请输入买入数量:');
                    
                    if (costPrice && amount) {
                        fetch('/position/update', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                code: code,
                                name: name,
                                cost_price: parseFloat(costPrice),
                                amount: parseInt(amount)
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                location.reload();
                            } else {
                                alert('添加持仓失败: ' + data.message);
                            }
                        });
                    }
                }

                document.getElementById('regenerateForm').addEventListener('submit', function(e) {
                    const regenerateBtn = document.getElementById('regenerateBtn');
                    const spinner = regenerateBtn.querySelector('.spinner-border');
                    // 禁用按钮并显示加载动画
                    regenerateBtn.disabled = true;
                    spinner.classList.remove('d-none');
                    regenerateBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 正在生成...';
                    // 显示提示信息
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-info alert-dismissible fade show mt-3';
                    alertDiv.innerHTML = `
                        <i class="bi bi-info-circle-fill"></i> 正在生成股票列表，请稍候...
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    this.parentNode.insertBefore(alertDiv, this.nextSibling);
                });
                </script>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>