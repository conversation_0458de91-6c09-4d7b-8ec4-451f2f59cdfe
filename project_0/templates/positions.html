<!doctype html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>持仓管理</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #fff;
            border-bottom: none;
            padding: 1.5rem;
        }
        .card-body {
            padding: 2rem;
        }
        .table {
            margin-bottom: 0;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .table td {
            vertical-align: middle;
        }
        /* 盈亏显示的统一样式 */
        .profit-value {
            color: #d60000 !important;  /* 盈利显示红色 */
        }
        .loss-value {
            color: #009933 !important;  /* 亏损显示绿色 */
        }
        /* 覆盖 Bootstrap 的文本颜色类 */
        .text-success {
            color: #009933 !important;  /* 亏损显示绿色 */
        }
        .text-danger {
            color: #d60000 !important;  /* 盈利显示红色 */
        }
        .btn-remove {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-remove:hover {
            background-color: #c82333;
        }
        .btn-back {
            background-color: #6c757d;
            border: none;
            padding: 0.5rem 2rem;
            border-radius: 25px;
            color: white;
            text-decoration: none;
        }
        .btn-back:hover {
            background-color: #5a6268;
            color: white;
        }
    </style>
</head>

{% macro profit_loss_display(value, unit='', size_class='') %}
    <span class="{{ size_class }} {% if value >= 0 %}profit-value{% else %}loss-value{% endif %}">
        {{ "%.2f"|format(value) }}{{ unit }}
    </span>
{% endmacro %}

<body>
    <div class="container">
        <div class="card">
            <div class="card-header text-center">
                <h1 class="mb-0">持仓管理</h1>
            </div>
            <div class="card-body">
                {% if positions %}
                    <div class="mb-4">
                        <div class="alert alert-light border shadow-sm" style="font-size:1.1rem;">
                            <!-- 第一行：基础资产信息 -->
                            <div class="row text-center mb-3">
                                <div class="col">
                                    <div class="mb-2">初始资金</div>
                                    <div class="h5 mb-0">{{ '%.2f'|format(initial_capital) }} 元</div>
                                </div>
                                <div class="col">
                                    <div class="mb-2">现金余额</div>
                                    <div class="h5 mb-0">{{ '%.2f'|format(current_cash) }} 元</div>
                                </div>
                                <div class="col">
                                    <div class="mb-2">持仓市值</div>
                                    <div class="h5 mb-0">{{ '%.2f'|format(total_value) }} 元</div>
                                </div>
                                <div class="col">
                                    <div class="mb-2">总资产</div>
                                    <div class="h5 mb-0 text-primary">{{ '%.2f'|format(total_assets) }} 元</div>
                                </div>
                            </div>

                            <!-- 第二行：盈亏信息 -->
                            <div class="row text-center">
                                <div class="col">
                                    <div class="mb-2">已实现盈亏</div>
                                    <div class="h5 mb-0">
                                        {{ profit_loss_display(realized_profit, ' 元', 'h5') }}
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="mb-2">未实现盈亏</div>
                                    <div class="h5 mb-0">
                                        {{ profit_loss_display(unrealized_profit, ' 元', 'h5') }}
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="mb-2">总盈亏</div>
                                    <div class="h4 mb-0">
                                        {{ profit_loss_display(total_profit, ' 元', 'h4') }}
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="mb-2">总盈亏比例</div>
                                    <div class="h4 mb-0">
                                        {{ profit_loss_display(total_profit_pct, '%', 'h4') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
                
                <!-- 交易记录卡片 -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center" 
                         style="cursor: pointer;" 
                         onclick="toggleTransactions()">
                        <h5 class="mb-0">交易记录</h5>
                        <span class="badge bg-light text-dark" id="transaction-count">0</span>
                    </div>
                    <div class="card-body" id="transactions-body" style="display: none;">
                        <!-- 翻页控制 -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <label for="pageSize" class="form-label me-2">每页显示：</label>
                                <select id="pageSize" class="form-select form-select-sm d-inline-block w-auto" onchange="changePageSize()">
                                    <option value="10">10条</option>
                                    <option value="20" selected>20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                            <div id="pagination-info" class="text-muted">
                                <!-- 分页信息 -->
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover" id="transactions-table">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>股票</th>
                                        <th>操作</th>
                                        <th>价格</th>
                                        <th>数量</th>
                                        <th>金额</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 交易记录将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页按钮 -->
                        <nav aria-label="交易记录分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>

                {% if positions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>代码</th>
                                    <th>名称</th>
                                    <th>持仓数量</th>
                                    <th>成本价</th>
                                    <th>当前价</th>
                                    <th>持仓市值</th>
                                    <th>盈亏金额</th>
                                    <th>盈亏比例</th>
                                    <th>持仓天数</th>
                                    <th>买入日期</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for pos in positions %}
                                    <tr data-code="{{ pos.code }}">
                                        <td>{{ pos.code }}</td>
                                        <td>{{ pos.name }}</td>
                                        <td>{{ pos.amount }}</td>
                                        <td>{{ "%.2f"|format(pos.cost_price) }}</td>
                                        <td>{{ "%.2f"|format(pos.current_price) }}</td>
                                        <td>{{ "%.2f"|format(pos.current_value) }}</td>
                                        <td>{{ profit_loss_display(pos.profit) }}</td>
                                        <td>{{ profit_loss_display(pos.profit_pct, '%') }}</td>
                                        <td>{{ pos.holding_days }}</td>
                                        <td>{{ pos.buy_date }}</td>
                                        <td>
                                            <button onclick="toggleEditForm('{{ pos.code }}')" class="btn btn-primary btn-sm">编辑</button>
                                            <button onclick="removePosition('{{ pos.code }}')" class="btn btn-danger btn-sm">移除</button>
                                            <div id="edit-form-{{ pos.code }}" class="position-info" style="display: none; margin-top: 10px;">
                                                <div class="input-group mb-2">
                                                    <span class="input-group-text">成本价</span>
                                                    <input type="number" id="cost-price-{{ pos.code }}" value="{{ pos.cost_price }}" step="0.01" class="form-control" style="width: 100px;">
                                                </div>
                                                <div class="input-group mb-2">
                                                    <span class="input-group-text">买入日期</span>
                                                    <input type="date" id="buy-date-{{ pos.code }}" value="{{ pos.buy_date }}" class="form-control" style="width: 150px;">
                                                </div>
                                                <div class="input-group mb-2">
                                                    <span class="input-group-text">数量</span>
                                                    <input type="number" id="amount-{{ pos.code }}" value="{{ pos.amount }}" step="100" min="100" class="form-control" style="width: 100px;">
                                                </div>
                                                <button onclick="savePosition('{{ pos.code }}', '{{ pos.name }}')" class="btn btn-success btn-sm">保存</button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        暂无持仓信息
                    </div>
                {% endif %}

                <div class="text-center mt-4">
                    <a href="{{ url_for('positions') }}" class="btn btn-primary">刷新持仓</a>
                    <a href="{{ url_for('index') }}" class="btn btn-back">返回</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <h2>持仓管理</h2>
        
        <!-- 调仓建议卡片 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">调仓建议</h5>
            </div>
            <div class="card-body">
                <div id="suggestions-container">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 持仓表格 -->

    <script>
        // 获取调仓建议
        function getRebalanceSuggestions() {
            fetch('/rebalance_suggestions')
                .then(response => response.json())
                .then(suggestions => {
                    const container = document.getElementById('suggestions-container');
                    if (!suggestions || suggestions.length === 0) {
                        container.innerHTML = '<div class="alert alert-info">暂无调仓建议</div>';
                        return;
                    }
                    
                    let html = '';
                    
                    // 显示系统消息
                    const systemMessages = suggestions.filter(s => s.type === 'system');
                    if (systemMessages.length > 0) {
                        html += '<div class="alert alert-primary mb-3">';
                        systemMessages.forEach(msg => {
                            html += `<div>${msg.message}</div>`;
                        });
                        html += '</div>';
                    }
                    
                    // 显示错误消息
                    const errorMessages = suggestions.filter(s => s.type === 'error');
                    if (errorMessages.length > 0) {
                        html += '<div class="alert alert-danger mb-3">';
                        errorMessages.forEach(msg => {
                            html += `<div>${msg.message}</div>`;
                        });
                        html += '</div>';
                    }
                    
                    // 显示股票建议
                    const stockSuggestions = suggestions.filter(s => s.type !== 'system' && s.type !== 'error');
                    if (stockSuggestions.length > 0) {
                        html += '<div class="table-responsive"><table class="table table-hover">';
                        html += '<thead><tr><th>股票</th><th>建议</th><th>原因</th><th>现价</th><th>成本</th><th>盈亏</th><th>操作</th></tr></thead>';
                        html += '<tbody>';
                        
                        stockSuggestions.forEach(suggestion => {
                            const actionClass = {
                                '卖出': 'danger',
                                '持有': 'success',
                                '关注': 'warning',
                                '买入': 'primary'
                            }[suggestion.action] || 'info';
                            
                            const typeClass = {
                                'stop_loss': 'table-danger',
                                'limit_up': 'table-warning',
                                'volume': 'table-info',
                                'not_in_list': 'table-danger',
                                'buy': 'table-success'
                            }[suggestion.type] || '';

                            const profitDisplay = suggestion.profit_pct ? 
                                `${suggestion.profit_pct.toFixed(2)}%` : '';

                            html += `<tr class="${typeClass}">`;
                            html += `<td>${suggestion.code} ${suggestion.name}</td>`;
                            html += `<td><span class="badge bg-${actionClass}">${suggestion.action}</span></td>`;
                            html += `<td>${suggestion.reason}</td>`;
                            html += `<td>${suggestion.current_price ? suggestion.current_price.toFixed(2) : ''}</td>`;
                            html += `<td>${suggestion.cost_price ? suggestion.cost_price.toFixed(2) : ''}</td>`;
                            html += `<td>${profitDisplay}</td>`;
                            html += `<td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="editPosition('${suggestion.code}')">
                                            编辑
                                        </button>
                                        ${suggestion.action === '卖出' || suggestion.action === '买入' ? 
                                            `<button type="button" class="btn btn-sm btn-outline-${actionClass}" 
                                                    onclick="executeAction('${suggestion.code}', '${suggestion.action}')">
                                                执行${suggestion.action}
                                            </button>` : ''}
                                    </div>
                                   </td>`;
                            html += '</tr>';
                        });
                        
                        html += '</tbody></table></div>';
                    } else {
                        html += '<div class="alert alert-info">暂无调仓建议</div>';
                    }

                    // 添加编辑模态框
                    html += `
                    <div class="modal fade" id="editPositionModal" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">编辑持仓</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="editPositionForm">
                                        <input type="hidden" id="editStockCode">
                                        <div class="mb-3">
                                            <label class="form-label">成本价</label>
                                            <input type="number" class="form-control" id="editCostPrice" step="0.01">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">持仓数量</label>
                                            <input type="number" class="form-control" id="editQuantity">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">备注</label>
                                            <textarea class="form-control" id="editNotes" rows="3"></textarea>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                    <button type="button" class="btn btn-primary" onclick="savePositionModal()">保存</button>
                                </div>
                            </div>
                        </div>
                    </div>`;

                    document.getElementById('suggestions-container').innerHTML = html;

                    // 添加JavaScript函数
                    const scriptElement = document.createElement('script');
                    scriptElement.innerHTML = `
                        function editPosition(stockCode) {
                            document.getElementById('editStockCode').value = stockCode;
                            // 获取当前持仓信息
                            fetch('/api/position/' + stockCode)
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        document.getElementById('editCostPrice').value = data.cost_price || '';
                                        document.getElementById('editQuantity').value = data.quantity || '';
                                        document.getElementById('editNotes').value = data.notes || '';
                                        new bootstrap.Modal(document.getElementById('editPositionModal')).show();
                                    } else {
                                        alert('获取持仓信息失败：' + data.message);
                                    }
                                })
                                .catch(error => {
                                    console.error('获取持仓信息失败:', error);
                                    alert('获取持仓信息失败，请重试');
                                });
                        }

                        function savePositionModal() {
                            const stockCode = document.getElementById('editStockCode').value;
                            const costPrice = document.getElementById('editCostPrice').value;
                            const quantity = document.getElementById('editQuantity').value;

                            if (!costPrice || !quantity) {
                                alert('请填写成本价和数量');
                                return;
                            }

                            const data = {
                                cost_price: parseFloat(costPrice),
                                quantity: parseInt(quantity),
                                notes: document.getElementById('editNotes').value
                            };

                            fetch('/api/position/update/' + stockCode, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify(data)
                            })
                            .then(response => response.json())
                            .then(result => {
                                if (result.success) {
                                    bootstrap.Modal.getInstance(document.getElementById('editPositionModal')).hide();
                                    alert('保存成功');
                                    // 刷新页面
                                    window.location.reload();
                                } else {
                                    alert('保存失败：' + result.message);
                                }
                            })
                            .catch(error => {
                                console.error('保存失败:', error);
                                alert('保存失败，请重试');
                            });
                        }

                        function executeAction(stockCode, action) {
                            let requestData = {
                                code: stockCode,
                                action: action
                            };

                            // 如果是买入操作，询问买入数量
                            if (action === '买入') {
                                const amount = prompt('请输入买入数量（股）:', '100');
                                if (!amount || isNaN(amount) || parseInt(amount) <= 0) {
                                    alert('请输入有效的买入数量');
                                    return;
                                }
                                requestData.amount = parseInt(amount);
                            }

                            if (confirm('确定要执行' + action + '操作吗？')) {
                                fetch('/api/execute_action', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify(requestData)
                                })
                                .then(response => response.json())
                                .then(result => {
                                    if (result.success) {
                                        alert(result.message);
                                        // 刷新页面
                                        window.location.reload();
                                    } else {
                                        alert('执行失败：' + result.message);
                                    }
                                })
                                .catch(error => {
                                    console.error('执行操作失败:', error);
                                    alert('执行操作失败，请重试');
                                });
                            }
                        }
                    `;
                    document.body.appendChild(scriptElement);
                })
                .catch(error => {
                    console.error('获取调仓建议失败:', error);
                    document.getElementById('suggestions-container').innerHTML = 
                        '<div class="alert alert-danger">获取调仓建议失败，请刷新页面重试</div>';
                });
        }

        // 页面加载时获取调仓建议和交易记录
        document.addEventListener('DOMContentLoaded', function() {
            getRebalanceSuggestions();
            getTransactions();

            // 根据当前时间设置不同的刷新频率
            const now = new Date();
            const hour = now.getHours();
            const minute = now.getMinutes();

            let refreshInterval;
            if (hour === 9 || hour === 14) {  // 开盘和收盘前
                refreshInterval = 5 * 60 * 1000;  // 5分钟
            } else if (hour === 10 && minute < 30) {  // 调仓时间
                refreshInterval = 1 * 60 * 1000;  // 1分钟
            } else {
                refreshInterval = 15 * 60 * 1000;  // 15分钟
            }

            setInterval(getRebalanceSuggestions, refreshInterval);
            // 每5分钟刷新一次交易记录
            setInterval(getTransactions, 5 * 60 * 1000);

            // 恢复上次的显示状态
            const wasVisible = localStorage.getItem('transactionsVisible') === 'true';
            if (wasVisible) {
                document.getElementById('transactions-body').style.display = 'block';
            }
        });

        function toggleEditForm(code) {
            const form = document.getElementById(`edit-form-${code}`);
            form.style.display = form.style.display === 'none' ? 'block' : 'none';
        }

        function savePosition(code, name) {
            const costPrice = document.getElementById(`cost-price-${code}`).value;
            const buyDate = document.getElementById(`buy-date-${code}`).value;
            const amount = document.getElementById(`amount-${code}`).value;
            
            if (!costPrice || !buyDate || !amount) {
                alert('请填写所有字段');
                return;
            }

            fetch('/position/edit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    code: code,
                    name: name,
                    cost_price: parseFloat(costPrice),
                    buy_date: buyDate,
                    amount: parseInt(amount)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // 更新当前行的数据
                    const row = document.querySelector(`tr[data-code="${code}"]`);
                    if (row) {
                        row.querySelector('td:nth-child(4)').textContent = parseFloat(costPrice).toFixed(2);
                        row.querySelector('td:nth-child(3)').textContent = amount;
                        row.querySelector('td:nth-child(10)').textContent = buyDate;
                    }
                    // 隐藏编辑表单
                    toggleEditForm(code);
                    // 刷新页面以更新其他计算值（如盈亏等）
                    window.location.reload();
                } else {
                    alert('更新持仓失败: ' + data.message);
                }
            });
        }

        function removePosition(code) {
            if (confirm('确定要移除该持仓吗？')) {
                fetch('/position/remove', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // 移除当前行
                        const row = document.querySelector(`tr[data-code="${code}"]`);
                        if (row) {
                            row.remove();
                        }
                        // 如果没有持仓了，显示提示信息
                        if (document.querySelectorAll('tbody tr').length === 0) {
                            const tbody = document.querySelector('tbody');
                            tbody.innerHTML = `
                                <tr>
                                    <td colspan="11" class="text-center">
                                        <div class="alert alert-info">
                                            暂无持仓信息
                                        </div>
                                    </td>
                                </tr>
                            `;
                        }
                    } else {
                        alert('移除持仓失败: ' + data.message);
                    }
                });
            }
        }

        // 交易记录分页变量
        let allTransactions = [];
        let currentPage = 1;
        let pageSize = 20;

        // 获取交易记录
        function getTransactions() {
            fetch('/transactions')
                .then(response => response.json())
                .then(transactions => {
                    allTransactions = transactions.sort((a, b) => new Date(b.date) - new Date(a.date));
                    const countBadge = document.getElementById('transaction-count');
                    countBadge.textContent = allTransactions.length;

                    if (allTransactions.length === 0) {
                        document.querySelector('#transactions-table tbody').innerHTML =
                            '<tr><td colspan="6" class="text-center">暂无交易记录</td></tr>';
                        document.getElementById('pagination').innerHTML = '';
                        document.getElementById('pagination-info').innerHTML = '';
                        return;
                    }

                    renderTransactions();
                })
                .catch(error => {
                    console.error('获取交易记录失败:', error);
                    document.querySelector('#transactions-table tbody').innerHTML =
                        '<tr><td colspan="6" class="text-center text-danger">获取交易记录失败</td></tr>';
                });
        }

        // 渲染交易记录
        function renderTransactions() {
            const tbody = document.querySelector('#transactions-table tbody');
            const totalPages = Math.ceil(allTransactions.length / pageSize);
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const currentTransactions = allTransactions.slice(startIndex, endIndex);

            let html = '';
            currentTransactions.forEach(trans => {
                const actionClass = trans.action === 'buy' ? 'success' : 'danger';
                const actionText = trans.action === 'buy' ? '买入' : '卖出';

                html += `
                    <tr>
                        <td>${trans.date}</td>
                        <td>${trans.name} (${trans.code})</td>
                        <td><span class="badge bg-${actionClass}">${actionText}</span></td>
                        <td>${trans.price.toFixed(2)}</td>
                        <td>${trans.amount}</td>
                        <td>${trans.value.toFixed(2)}</td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
            renderPagination(totalPages);
            updatePaginationInfo();
        }

        // 渲染分页按钮
        function renderPagination(totalPages) {
            const pagination = document.getElementById('pagination');
            let html = '';

            // 上一页按钮
            html += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>
                </li>
            `;

            // 页码按钮
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            if (startPage > 1) {
                html += '<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>';
                if (startPage > 2) {
                    html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
                }
                html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a></li>`;
            }

            // 下一页按钮
            html += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>
                </li>
            `;

            pagination.innerHTML = html;
        }

        // 更新分页信息
        function updatePaginationInfo() {
            const totalPages = Math.ceil(allTransactions.length / pageSize);
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, allTransactions.length);

            document.getElementById('pagination-info').innerHTML =
                `显示 ${startIndex}-${endIndex} 条，共 ${allTransactions.length} 条记录，第 ${currentPage}/${totalPages} 页`;
        }

        // 切换页面
        function changePage(page) {
            const totalPages = Math.ceil(allTransactions.length / pageSize);
            if (page < 1 || page > totalPages) return;

            currentPage = page;
            renderTransactions();
        }

        // 改变每页显示数量
        function changePageSize() {
            pageSize = parseInt(document.getElementById('pageSize').value);
            currentPage = 1; // 重置到第一页
            renderTransactions();
        }

        // 切换交易记录的显示/隐藏
        function toggleTransactions() {
            const body = document.getElementById('transactions-body');
            const isHidden = body.style.display === 'none';
            body.style.display = isHidden ? 'block' : 'none';
            
            // 保存状态到 localStorage
            localStorage.setItem('transactionsVisible', isHidden);
        }


    </script>
</body>
</html> 