#!/usr/bin/env python3
"""
测试app.py中修改后的总盈亏比例计算逻辑
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入配置
from config import INITIAL_CAPITAL

def test_app_calculation():
    """测试app.py中的计算逻辑"""
    
    print("=== 测试app.py中的总盈亏比例计算 ===\n")
    print(f"初始资金配置: {INITIAL_CAPITAL:,.2f}\n")
    
    # 模拟交易数据和计算逻辑（简化版本）
    def calculate_profit_percentage(realized_profit, unrealized_profit, initial_capital):
        """计算总盈亏比例"""
        total_profit = realized_profit + unrealized_profit
        if initial_capital > 0:
            total_profit_pct = (total_profit / initial_capital) * 100
        else:
            total_profit_pct = 0.0
        return total_profit, total_profit_pct
    
    # 测试场景1：模拟您当前的数据
    print("场景1：模拟实际数据")
    print("-" * 40)
    
    # 从之前的调试信息可以看到：
    # Debug - 已实现盈亏: 7889.00
    # Debug - 未实现盈亏: 1272.00
    realized_profit = 7889.00
    unrealized_profit = 1272.00
    
    total_profit, total_profit_pct = calculate_profit_percentage(
        realized_profit, unrealized_profit, INITIAL_CAPITAL
    )
    
    print(f"已实现盈亏: {realized_profit:,.2f}")
    print(f"未实现盈亏: {unrealized_profit:,.2f}")
    print(f"总盈亏: {total_profit:,.2f}")
    print(f"总盈亏比例: {total_profit_pct:.2f}%")
    print()
    
    # 对比之前错误的计算方法
    print("对比：之前错误的计算方法")
    print("-" * 40)
    
    # 模拟之前的错误计算（基于总资产）
    current_cash = 60000  # 假设现金余额等于初始资金
    current_stock_value = 68662  # 从调试信息中的持仓市值
    total_assets = current_cash + current_stock_value
    
    wrong_total_profit = total_assets - INITIAL_CAPITAL
    wrong_total_profit_pct = (wrong_total_profit / INITIAL_CAPITAL) * 100
    
    print(f"现金余额: {current_cash:,.2f}")
    print(f"持仓市值: {current_stock_value:,.2f}")
    print(f"总资产: {total_assets:,.2f}")
    print(f"错误的总盈亏: {wrong_total_profit:,.2f}")
    print(f"错误的总盈亏比例: {wrong_total_profit_pct:.2f}%")
    print()
    
    print("=== 结果对比 ===")
    print(f"正确方法 - 总盈亏比例: {total_profit_pct:.2f}%")
    print(f"错误方法 - 总盈亏比例: {wrong_total_profit_pct:.2f}%")
    print(f"差异: {abs(total_profit_pct - wrong_total_profit_pct):.2f}个百分点")
    print()
    
    print("=== 说明 ===")
    print("正确的计算方法：")
    print("- 只关注实际的盈亏金额")
    print("- 不受现金余额影响")
    print("- 真实反映投资收益率")
    print()
    print("错误的计算方法问题：")
    print("- 将现金余额计入收益")
    print("- 夸大了实际收益率")
    print("- 不能准确反映投资表现")

if __name__ == "__main__":
    test_app_calculation()
