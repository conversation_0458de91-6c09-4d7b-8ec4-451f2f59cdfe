#!/usr/bin/env python3
"""
测试现金管理器功能的脚本
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入配置和现金管理器
from config import INITIAL_CAPITAL

def test_cash_manager():
    """测试现金管理器功能"""
    
    print("=== 测试现金管理器功能 ===\n")
    
    # 创建现金管理器类（简化版本，用于测试）
    class TestCashManager:
        def __init__(self):
            self.cash_file = 'test_cash_balance.json'
            self.cash_balance = self.load_cash_balance()
        
        def load_cash_balance(self):
            """加载现金余额"""
            try:
                if os.path.exists(self.cash_file):
                    with open(self.cash_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        return data.get('cash_balance', INITIAL_CAPITAL)
                else:
                    return INITIAL_CAPITAL
            except Exception as e:
                print(f"加载现金余额失败: {e}")
                return INITIAL_CAPITAL
        
        def save_cash_balance(self):
            """保存现金余额"""
            try:
                data = {
                    'cash_balance': self.cash_balance,
                    'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                with open(self.cash_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"保存现金余额失败: {e}")
        
        def update_cash(self, amount, operation='add'):
            """更新现金余额"""
            if operation == 'add':
                self.cash_balance += amount
            elif operation == 'subtract':
                self.cash_balance -= amount
            
            self.save_cash_balance()
            print(f"现金余额更新: {operation} {amount:.2f}, 当前余额: {self.cash_balance:.2f}")
        
        def get_cash_balance(self):
            """获取当前现金余额"""
            return self.cash_balance
        
        def reset_cash_balance(self):
            """重置现金余额为初始资金"""
            self.cash_balance = INITIAL_CAPITAL
            self.save_cash_balance()
            print(f"现金余额已重置为初始资金: {INITIAL_CAPITAL}")
    
    # 创建测试实例
    cash_manager = TestCashManager()
    
    print(f"初始资金配置: {INITIAL_CAPITAL:,.2f}")
    print(f"当前现金余额: {cash_manager.get_cash_balance():,.2f}")
    print()
    
    # 测试买入操作（减少现金）
    print("测试买入操作（减少现金）:")
    cash_manager.update_cash(30000, 'subtract')  # 买入3万股票
    print()
    
    # 测试卖出操作（增加现金）
    print("测试卖出操作（增加现金）:")
    cash_manager.update_cash(35000, 'add')  # 卖出获得3.5万
    print()
    
    # 再次买入
    print("再次买入:")
    cash_manager.update_cash(25000, 'subtract')  # 买入2.5万股票
    print()
    
    # 计算总盈亏比例
    print("计算总盈亏比例:")
    current_cash = cash_manager.get_cash_balance()
    current_stock_value = 28000  # 假设当前持仓市值2.8万
    total_assets = current_cash + current_stock_value
    
    total_profit = total_assets - INITIAL_CAPITAL
    total_profit_pct = (total_profit / INITIAL_CAPITAL) * 100
    
    print(f"当前现金余额: {current_cash:,.2f}")
    print(f"当前持仓市值: {current_stock_value:,.2f}")
    print(f"当前总资产: {total_assets:,.2f}")
    print(f"总盈亏: {total_profit:,.2f}")
    print(f"总盈亏比例: {total_profit_pct:.2f}%")
    print()
    
    # 重置测试
    print("重置现金余额:")
    cash_manager.reset_cash_balance()
    print()
    
    # 清理测试文件
    if os.path.exists('test_cash_balance.json'):
        os.remove('test_cash_balance.json')
        print("测试文件已清理")
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    test_cash_manager()
