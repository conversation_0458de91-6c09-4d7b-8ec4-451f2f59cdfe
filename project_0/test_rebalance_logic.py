#!/usr/bin/env python3
"""
测试调仓建议逻辑的脚本
"""

from datetime import datetime
import requests
import json

def test_rebalance_day():
    """测试调仓日判断"""
    today = datetime.now()
    weekday = today.weekday()  # 0=Monday, 1=Tuesday, ..., 6=Sunday
    
    print(f"今天是: {today.strftime('%Y-%m-%d %A')}")
    print(f"星期几: {weekday} (0=周一, 1=周二, ..., 6=周日)")
    print(f"是否是调仓日(周二): {weekday == 1}")
    
    return weekday == 1

def test_rebalance_api():
    """测试调仓建议API"""
    try:
        url = 'http://localhost:5000/rebalance_suggestions'
        response = requests.get(url)
        
        print(f"\nAPI响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            suggestions = response.json()
            print(f"调仓建议数量: {len(suggestions)}")
            
            for i, suggestion in enumerate(suggestions):
                print(f"\n建议 {i+1}:")
                print(f"  类型: {suggestion.get('type', 'N/A')}")
                print(f"  消息: {suggestion.get('message', 'N/A')}")
                if 'code' in suggestion:
                    print(f"  股票: {suggestion.get('code')} {suggestion.get('name', '')}")
                    print(f"  操作: {suggestion.get('action', 'N/A')}")
                    print(f"  原因: {suggestion.get('reason', 'N/A')}")
                    if 'current_price' in suggestion:
                        print(f"  当前价格: {suggestion.get('current_price')}")
                    if 'profit_pct' in suggestion:
                        print(f"  盈亏比例: {suggestion.get('profit_pct'):.2f}%")
        else:
            print(f"API请求失败: {response.text}")
            
    except Exception as e:
        print(f"测试API时出错: {str(e)}")

def check_session_data():
    """检查session数据是否存在"""
    try:
        # 这个需要在有session的环境中运行
        print("\n检查session数据...")
        print("注意: 这个检查需要在Flask应用中运行，或者手动检查")
        print("需要确认:")
        print("1. 是否已经运行过评分策略")
        print("2. session中是否有table_data")
        print("3. table_data中是否包含'技术打分'列")
        
    except Exception as e:
        print(f"检查session数据时出错: {str(e)}")

def analyze_rebalance_logic():
    """分析调仓逻辑"""
    print("\n=== 调仓建议逻辑分析 ===")
    print("调仓建议的触发条件:")
    print("1. 必须是周二（调仓日）")
    print("2. 必须已经运行过评分策略（session中有table_data）")
    print("3. 评分数据中必须有'技术打分'列")
    print("\n调仓建议的生成规则:")
    print("对于当前持仓:")
    print("  - 不在候选名单中 → 建议卖出")
    print("  - 跌出前10名 → 建议卖出") 
    print("  - 跌出前6名 → 建议持有（警告）")
    print("  - 亏损超过10% → 建议卖出（止损）")
    print("  - 涨停 → 建议持有")
    print("对于未持仓股票:")
    print("  - 前6名中未持有的股票 → 建议买入")

if __name__ == '__main__':
    print("=== 调仓建议测试 ===")
    
    # 测试调仓日判断
    is_rebalance_day = test_rebalance_day()
    
    if not is_rebalance_day:
        print("\n❌ 今天不是调仓日，所以不会显示调仓建议")
        print("调仓建议只在每周二显示")
    else:
        print("\n✅ 今天是调仓日，应该显示调仓建议")
        
        # 测试API
        test_rebalance_api()
    
    # 检查其他可能的问题
    check_session_data()
    
    # 分析逻辑
    analyze_rebalance_logic()
    
    print("\n=== 可能的问题排查 ===")
    print("如果今天是周二但没有调仓建议，请检查:")
    print("1. 是否已经运行过评分策略？")
    print("2. 浏览器控制台是否有JavaScript错误？")
    print("3. 服务器日志中是否有错误信息？")
    print("4. 网络请求是否成功？(F12 -> Network)")
