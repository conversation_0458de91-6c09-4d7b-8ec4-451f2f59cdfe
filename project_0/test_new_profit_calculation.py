#!/usr/bin/env python3
"""
测试修复后的总盈亏比例计算逻辑的脚本
"""

def test_new_profit_calculation():
    """测试新的盈亏计算逻辑"""
    
    print("=== 测试新的总盈亏比例计算逻辑 ===\n")
    
    # 模拟初始资金
    INITIAL_CAPITAL = 100000  # 10万初始资金
    
    # 测试场景1：简单买入持有
    print("场景1：简单买入持有")
    print("-" * 40)
    current_cash = 50000  # 剩余现金5万
    current_stock_value = 60000  # 持仓市值6万
    total_assets = current_cash + current_stock_value  # 总资产11万
    
    # 新的计算方法
    new_total_profit = total_assets - INITIAL_CAPITAL
    new_total_profit_pct = (new_total_profit / INITIAL_CAPITAL) * 100
    
    print(f"初始资金: {INITIAL_CAPITAL:,.2f}")
    print(f"当前现金: {current_cash:,.2f}")
    print(f"持仓市值: {current_stock_value:,.2f}")
    print(f"当前总资产: {total_assets:,.2f}")
    print(f"总盈亏: {new_total_profit:,.2f}")
    print(f"总盈亏比例: {new_total_profit_pct:.2f}%")
    print()
    
    # 测试场景2：多次买卖后的情况
    print("场景2：多次买卖后的情况")
    print("-" * 40)
    
    # 模拟交易历史
    transactions = [
        {'action': 'buy', 'value': 30000, 'amount': 3000, 'code': 'A'},   # 买入3万股票A
        {'action': 'sell', 'value': 35000, 'amount': 3000, 'code': 'A'},  # 卖出获得3.5万
        {'action': 'buy', 'value': 40000, 'amount': 4000, 'code': 'B'},   # 买入4万股票B
    ]
    
    # 当前状态
    current_cash_2 = 65000  # 现金：10万 - 3万 + 3.5万 - 4万 = 6.5万
    current_stock_value_2 = 45000  # 股票B现值4.5万
    total_assets_2 = current_cash_2 + current_stock_value_2  # 总资产11万
    
    # 旧方法（基于历史投资总额）
    total_investment = 30000 + 40000  # 历史总投资7万
    old_total_profit = total_assets_2 - total_investment  # 这个计算是错误的
    old_total_profit_pct = (old_total_profit / total_investment) * 100 if total_investment > 0 else 0
    
    # 新方法（基于初始资金）
    new_total_profit_2 = total_assets_2 - INITIAL_CAPITAL
    new_total_profit_pct_2 = (new_total_profit_2 / INITIAL_CAPITAL) * 100
    
    print(f"交易历史:")
    for i, trans in enumerate(transactions, 1):
        print(f"  {i}. {trans['action']} {trans['code']}: {trans['value']:,.2f}")
    
    print(f"\n当前状态:")
    print(f"初始资金: {INITIAL_CAPITAL:,.2f}")
    print(f"当前现金: {current_cash_2:,.2f}")
    print(f"持仓市值: {current_stock_value_2:,.2f}")
    print(f"当前总资产: {total_assets_2:,.2f}")
    
    print(f"\n旧计算方法（错误）:")
    print(f"历史总投资: {total_investment:,.2f}")
    print(f"旧总盈亏: {old_total_profit:,.2f}")
    print(f"旧总盈亏比例: {old_total_profit_pct:.2f}%")
    
    print(f"\n新计算方法（正确）:")
    print(f"新总盈亏: {new_total_profit_2:,.2f}")
    print(f"新总盈亏比例: {new_total_profit_pct_2:.2f}%")
    print()
    
    # 测试场景3：亏损情况
    print("场景3：亏损情况")
    print("-" * 40)
    current_cash_3 = 40000  # 现金4万
    current_stock_value_3 = 45000  # 持仓市值4.5万
    total_assets_3 = current_cash_3 + current_stock_value_3  # 总资产8.5万
    
    new_total_profit_3 = total_assets_3 - INITIAL_CAPITAL
    new_total_profit_pct_3 = (new_total_profit_3 / INITIAL_CAPITAL) * 100
    
    print(f"初始资金: {INITIAL_CAPITAL:,.2f}")
    print(f"当前现金: {current_cash_3:,.2f}")
    print(f"持仓市值: {current_stock_value_3:,.2f}")
    print(f"当前总资产: {total_assets_3:,.2f}")
    print(f"总盈亏: {new_total_profit_3:,.2f}")
    print(f"总盈亏比例: {new_total_profit_pct_3:.2f}%")
    print()
    
    print("=== 总结 ===")
    print("新的计算方法优势：")
    print("1. 基于初始资金计算，不会因为多次交易而累积投资额")
    print("2. 直观反映相对于初始资金的真实收益率")
    print("3. 总资产 = 现金余额 + 持仓市值，更准确反映当前财务状况")
    print("4. 总盈亏比例 = (当前总资产 - 初始资金) / 初始资金 * 100")

if __name__ == "__main__":
    test_new_profit_calculation()
