#!/usr/bin/env python3
"""
测试正确的总盈亏比例计算公式：(已实现盈亏+未实现盈亏)/初始资金
"""

def test_correct_profit_calculation():
    """测试正确的盈亏计算公式"""
    
    print("=== 测试正确的总盈亏比例计算公式 ===\n")
    print("公式：总盈亏比例 = (已实现盈亏 + 未实现盈亏) / 初始资金 * 100\n")
    
    # 使用用户修改后的初始资金
    INITIAL_CAPITAL = 60000  # 6万初始资金
    
    # 测试场景1：简单情况
    print("场景1：简单买入持有")
    print("-" * 40)
    realized_profit_1 = 0      # 已实现盈亏：0（没有卖出）
    unrealized_profit_1 = 5000 # 未实现盈亏：5000（持仓浮盈）
    
    total_profit_1 = realized_profit_1 + unrealized_profit_1
    total_profit_pct_1 = (total_profit_1 / INITIAL_CAPITAL) * 100
    
    print(f"初始资金: {INITIAL_CAPITAL:,.2f}")
    print(f"已实现盈亏: {realized_profit_1:,.2f}")
    print(f"未实现盈亏: {unrealized_profit_1:,.2f}")
    print(f"总盈亏: {total_profit_1:,.2f}")
    print(f"总盈亏比例: {total_profit_pct_1:.2f}%")
    print()
    
    # 测试场景2：有买卖交易
    print("场景2：有买卖交易")
    print("-" * 40)
    realized_profit_2 = 3000   # 已实现盈亏：3000（卖出获利）
    unrealized_profit_2 = 2000 # 未实现盈亏：2000（当前持仓浮盈）
    
    total_profit_2 = realized_profit_2 + unrealized_profit_2
    total_profit_pct_2 = (total_profit_2 / INITIAL_CAPITAL) * 100
    
    print(f"初始资金: {INITIAL_CAPITAL:,.2f}")
    print(f"已实现盈亏: {realized_profit_2:,.2f}")
    print(f"未实现盈亏: {unrealized_profit_2:,.2f}")
    print(f"总盈亏: {total_profit_2:,.2f}")
    print(f"总盈亏比例: {total_profit_pct_2:.2f}%")
    print()
    
    # 测试场景3：亏损情况
    print("场景3：亏损情况")
    print("-" * 40)
    realized_profit_3 = -1000  # 已实现盈亏：-1000（卖出亏损）
    unrealized_profit_3 = -2000 # 未实现盈亏：-2000（当前持仓浮亏）
    
    total_profit_3 = realized_profit_3 + unrealized_profit_3
    total_profit_pct_3 = (total_profit_3 / INITIAL_CAPITAL) * 100
    
    print(f"初始资金: {INITIAL_CAPITAL:,.2f}")
    print(f"已实现盈亏: {realized_profit_3:,.2f}")
    print(f"未实现盈亏: {unrealized_profit_3:,.2f}")
    print(f"总盈亏: {total_profit_3:,.2f}")
    print(f"总盈亏比例: {total_profit_pct_3:.2f}%")
    print()
    
    # 测试场景4：复杂交易情况
    print("场景4：复杂交易情况")
    print("-" * 40)
    realized_profit_4 = 8000   # 已实现盈亏：8000（多次买卖的净盈亏）
    unrealized_profit_4 = -1500 # 未实现盈亏：-1500（当前持仓浮亏）
    
    total_profit_4 = realized_profit_4 + unrealized_profit_4
    total_profit_pct_4 = (total_profit_4 / INITIAL_CAPITAL) * 100
    
    print(f"初始资金: {INITIAL_CAPITAL:,.2f}")
    print(f"已实现盈亏: {realized_profit_4:,.2f}")
    print(f"未实现盈亏: {unrealized_profit_4:,.2f}")
    print(f"总盈亏: {total_profit_4:,.2f}")
    print(f"总盈亏比例: {total_profit_pct_4:.2f}%")
    print()
    
    print("=== 公式说明 ===")
    print("这个公式的优势：")
    print("1. 简单直观：直接用盈亏金额除以初始资金")
    print("2. 不受交易次数影响：无论买卖多少次，基准始终是初始资金")
    print("3. 准确反映收益率：真实反映相对于初始投入的收益情况")
    print("4. 不需要考虑现金余额：只关注盈亏本身")
    print()
    print("已实现盈亏：通过买卖交易已经确定的盈亏")
    print("未实现盈亏：当前持仓相对于成本价的浮动盈亏")
    print("总盈亏比例：(已实现盈亏 + 未实现盈亏) / 初始资金 * 100")

if __name__ == "__main__":
    test_correct_profit_calculation()
