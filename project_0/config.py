# config.py

# 资金管理参数
INITIAL_CAPITAL = 60000  # 初始资金（6万）

# 市场参数
INDEX_CODE = "399101"  # 中小板指数代码
BENCHMARK_INDEX = INDEX_CODE  # 用于计算相对强度的基准指数

# 基础策略参数
POSITION_COUNT = 6      # 持仓数，默认6
REBALANCE_DAY = 2      # 调仓日（1-5分别代表周一到周五）
NEW_STOCK_DAYS_THRESHOLD = 540  # 次新股判断阈值（天）

# 技术指标参数
HISTORY_DAYS_FOR_COUNT = 365  # 统计涨跌停和大幅上涨次数的历史天数
POSITIVE_MOVE_THRESHOLD = 5.0 # 统计涨幅超过该阈值的次数
RECENT_DAYS_FOR_MOMENTUM = 30 # 计算近期动量的天数
SHORT_TERM_VOLUME_DAYS = 10   # 短期成交量统计天数
RECENT_DAYS_FOR_VOLUME = 30   # 计算近期日均成交额的天数
RELATIVE_STRENGTH_DAYS = 30   # 计算相对强度的天数
DAILY_LIMIT_THRESHOLD = 9.9   # 涨跌停判断阈值（百分比）

# 振幅指标参数
AMPLITUDE_DAYS = 20           # 振幅计算天数
AMPLITUDE_THRESHOLD = 7.0     # 显著振幅阈值

# 止损止盈信号参数
SIGNAL_PARAMS = {
    'ENABLE_SIGNALS': True,           # 是否启用信号生成
    'STOPLOSS_STRATEGY': 3,          # 止损策略类型：1-个股止损，2-大盘止损，3-联合止损
    'INDIVIDUAL_STOPLOSS': 0.88,     # 个股止损阈值（相对成本价的比例）
    'MARKET_STOPLOSS': 0.94,         # 大盘止损阈值（相对开盘价的比例）
    'PROFIT_TAKE': 2.0,              # 止盈阈值（相对成本价的倍数）
    'VOLUME_ALERT': {                 # 成交量异常信号
        'ENABLE': True,               # 是否启用成交量异常检测
        'HISTORY_DAYS': 120,          # 参考过去多少天的成交量
        'THRESHOLD_RATIO': 0.9        # 当日成交量超过历史最高的比例
    },
    'SPECIAL_PERIODS': {              # 特殊时期信号
        'ENABLE': True,               # 是否启用特殊时期检测
        'CLEAR_POSITION_MONTHS': ['04', '01']  # 空仓月份
    }
}

# 权重配置
WEIGHTS = {
    '年涨停': {'weight': 0.25, 'dir': 'high'},
    'gt_5': {'weight': 0.15, 'dir': 'high'},
    f'{RECENT_DAYS_FOR_MOMENTUM}日涨跌幅': {'weight': 0.15, 'dir': 'high'},
    '10日成交强度': {'weight': 0.15, 'dir': 'high'},
    f'{RELATIVE_STRENGTH_DAYS}日相对强度': {'weight': 0.10, 'dir': 'high'},
    '平均振幅': {'weight': 0.10, 'dir': 'high'},
    '显著振幅天数': {'weight': 0.05, 'dir': 'high'},
    '最大振幅': {'weight': 0.05, 'dir': 'high'}
}

# 跌停惩罚参数
PENALTY_WEIGHTS = {
    '0-3个月': 1.0,
    '3-6个月': 0.7,
    '6-12个月': 0.4
}
PENALTY_WEIGHT = 0.05

# 调仓建议展示配置
DISPLAY_PARAMS = {
    'SHOW_SCORE_DETAILS': True,      # 是否显示详细的评分信息
    'SHOW_VOLUME_ANALYSIS': True,    # 是否显示成交量分析
    'SHOW_TECHNICAL_INDICATORS': True,# 是否显示技术指标
    'RANKING_DISPLAY_COUNT': 20      # 展示排名的股票数量
} 