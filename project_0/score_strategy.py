import akshare as ak
import pandas as pd
from datetime import datetime, date, timedelta
import numpy as np

# --- 配置参数 ---
DAILY_LIMIT_THRESHOLD = 9.9 # 涨跌停判断阈值（百分比），例如 9.9 表示接近10%

# --- 技术/市场指标打分配置 ---
HISTORY_DAYS_FOR_COUNT = 365 # 统计涨跌停和大幅上涨次数的历史天数 (例如过去1年)
POSITIVE_MOVE_THRESHOLD = 5.0 # 统计涨幅超过该阈值的次数 (例如 5%)
RECENT_DAYS_FOR_MOMENTUM = 30 # 计算近期动量的天数 (例如近1个月)
RECENT_DAYS_FOR_VOLUME = 30 # 计算近期日均成交额的天数 (例如近1个月)
RELATIVE_STRENGTH_DAYS = 90 # 计算相对强度的天数 (例如近3个月)
BENCHMARK_INDEX = "399101" # 计算相对强度的基准指数 (改为创业板指, 399006，因为它和中小板同在深交所)
# 注意：如果使用中小板指 399101，请将上一行改为 BENCHMARK_INDEX = "399101"
# 注意：沪深300的代码在 ak.index_zh_a_hist 接口中通常是 "000300"，不是 "sh000300"

print("--- 股票技术/市场打分策略开始 ---")
print(f"涨跌停判断：计算涨跌幅 >= {DAILY_LIMIT_THRESHOLD}% 或 <= -{DAILY_LIMIT_THRESHOLD}%")
print("-" * 20)
print(f"活跃度统计周期：过去 {HISTORY_DAYS_FOR_COUNT} 天")
print(f"涨幅 > {POSITIVE_MOVE_THRESHOLD}% (非涨停) 统计")
print(f"涨跌停次数统计")
print(f"近期动量/成交量周期：过去 {RECENT_DAYS_FOR_MOMENTUM} 天")
print(f"相对强度周期：过去 {RELATIVE_STRENGTH_DAYS} 天 (对比 {BENCHMARK_INDEX})")
print("-" * 20)

# 1. 读取选出的股票列表
print("1. 读取选出的股票列表...")
try:
    # 确保 encoding 参数正确，取决于你的 CSV 保存格式
    selected_stocks = pd.read_csv("selected_stocks.csv", encoding="utf_8_sig")
    if selected_stocks.empty:
        print("错误：未获取到选出的股票数据，请检查 selected_stocks.csv 文件。")
        exit()
    # 确保股票代码是字符串，并补齐到6位
    selected_stocks['代码'] = selected_stocks['代码'].astype(str).str.zfill(6)
    print(f"   获取到 {len(selected_stocks)} 只股票。")
except Exception as e:
    print(f"读取选出的股票数据失败: {e}")
    exit()

# 确保待打分的股票列表中有股票
if selected_stocks.empty:
    print("\n没有股票可供分析和打分。")
else:
    print("\n2. 计算技术/市场指标...")

    # 获取历史数据的时间范围
    end_date_str = date.today().strftime('%Y%m%d')
    # 统计次数和相对强度需要较长历史，近期动量/成交量需要较短历史
    # 获取最长需要的天数 + 一些buffer (例如 30天)
    max_history_days = max(HISTORY_DAYS_FOR_COUNT, RECENT_DAYS_FOR_MOMENTUM, RECENT_DAYS_FOR_VOLUME, RELATIVE_STRENGTH_DAYS) + 30
    start_date_str = (date.today() - timedelta(days=max_history_days)).strftime('%Y%m%d')


    # 获取基准指数历史数据用于相对强度计算 (使用 ak.index_zh_a_hist)
    index_hist = None
    try:
        print(f"   获取基准指数 {BENCHMARK_INDEX} 历史数据 (使用 ak.index_zh_a_hist)...")
        # 使用 ak.index_zh_a_hist
        index_hist_raw = ak.index_zh_a_hist(symbol=BENCHMARK_INDEX, period="daily", start_date=start_date_str, end_date=end_date_str)
        if not index_hist_raw.empty:
            # 确保 '日期' 列存在且转换为 datetime 对象
            if '日期' in index_hist_raw.columns:
                 index_hist_raw['日期'] = pd.to_datetime(index_hist_raw['日期'])
                 # 确保按照日期升序，并设置为索引，只保留 '收盘' 价格
                 index_hist = index_hist_raw.set_index('日期')['收盘'].sort_index()
                 print("   基准指数历史数据获取成功。")
            else:
                 print(f"   警告: 基准指数 {BENCHMARK_INDEX} 历史数据中无'日期'列，无法处理。")
                 index_hist = None
        else:
             print(f"   警告: 未获取到基准指数 {BENCHMARK_INDEX} 历史数据或数据为空。")
             index_hist = None
    except Exception as e:
        print(f"   获取基准指数历史数据失败: {e}")
        index_hist = None # If fail, cannot calculate relative strength


    # 循环计算每个股票的指标
    metrics_list = []
    num_stocks_to_process = len(selected_stocks)
    for i, (index, row) in enumerate(selected_stocks.iterrows()):
        stock_code = row['代码']
        stock_name = row['名称']
        print(f"  - ({i+1}/{num_stocks_to_process}) 正在获取 {stock_code} ({stock_name}) 的历史数据并计算指标...")

        try:
            # 获取股票历史数据 (使用前复权 adjust="qfq")
            stock_hist_raw = ak.stock_zh_a_hist(symbol=stock_code, period="daily", start_date=start_date_str, end_date=end_date_str, adjust="qfq")
            if stock_hist_raw is None or stock_hist_raw.empty:
                print(f"     警告: 未获取到 {stock_code} 的历史数据或数据为空，跳过指标计算。")
                metrics_list.append({'代码': stock_code}) # Add with default NaN/0 metrics
                continue

            # 确保 '日期' 列存在且转换为 datetime 对象
            if '日期' in stock_hist_raw.columns:
                 stock_hist_raw['日期'] = pd.to_datetime(stock_hist_raw['日期'])
                 stock_hist = stock_hist_raw.set_index('日期').sort_index() # 确保按日期升序
            else:
                 print(f"     警告: {stock_code} 历史数据中无'日期'列，无法处理。")
                 metrics_list.append({'代码': stock_code})
                 continue


            # 确保有'涨跌幅'列，stock_zh_a_hist with adjust='qfq' 应该包含此列
            if '涨跌幅' not in stock_hist.columns:
                 print(f"     警告: {stock_code} 历史数据无'涨跌幅'列，部分指标计算可能不准确。")
                 stock_hist['涨跌幅'] = np.nan # Placeholder for metrics that rely on this


            metrics = {'代码': stock_code} # Use '代码' to match selected_stocks DataFrame


            # --- 计算历史波动次数 (过去 HISTORY_DAYS_FOR_COUNT 天) ---
            hist_count_start_date = pd.to_datetime(date.today() - timedelta(days=HISTORY_DAYS_FOR_COUNT))
            hist_count_data = stock_hist[stock_hist.index >= hist_count_start_date].copy()
            # Check for sufficient data points in the counting period
            if not hist_count_data.empty and '涨跌幅' in hist_count_data.columns and not hist_count_data['涨跌幅'].isnull().all():
                 # 涨停次数 (>= 9.9)
                 metrics['历史涨停次数'] = (hist_count_data['涨跌幅'] >= DAILY_LIMIT_THRESHOLD).sum()
                 # 跌停次数 (<= -9.9)
                 metrics['历史跌停次数'] = (hist_count_data['涨跌幅'] <= -DAILY_LIMIT_THRESHOLD).sum()
                 # 涨幅 > 5% 次数 (5 <=涨跌幅 < 9.9)
                 metrics['历史涨幅_gt_5_次数'] = ((hist_count_data['涨跌幅'] >= POSITIVE_MOVE_THRESHOLD) & (hist_count_data['涨跌幅'] < DAILY_LIMIT_THRESHOLD)).sum()
                 # 跌幅 < -5% 次数 (-9.9 < 涨跌幅 <= -5)
                 metrics['历史跌幅_lt_neg5_次数'] = ((hist_count_data['涨跌幅'] <= -POSITIVE_MOVE_THRESHOLD) & (hist_count_data['涨跌幅'] > -DAILY_LIMIT_THRESHOLD)).sum()
            else:
                 metrics['历史涨停次数'] = 0
                 metrics['历史跌停次数'] = 0
                 metrics['历史涨幅_gt_5_次数'] = 0
                 metrics['历史跌幅_lt_neg5_次数'] = 0
                 print(f"     警告: {stock_code} 在过去 {HISTORY_DAYS_FOR_COUNT} 天内历史数据不足或涨跌幅缺失，波动次数设为0。")


            # --- 计算近期动量 (过去 RECENT_DAYS_FOR_MOMENTUM 天) ---
            momentum_start_date = pd.to_datetime(date.today() - timedelta(days=RECENT_DAYS_FOR_MOMENTUM))
            momentum_data = stock_hist[stock_hist.index >= momentum_start_date].copy()
            if not momentum_data.empty and len(momentum_data) > 1 and '收盘' in momentum_data.columns:
                 first_price = momentum_data['收盘'].iloc[0]
                 last_price = momentum_data['收盘'].iloc[-1]
                 if first_price != 0:
                      metrics[f'近{RECENT_DAYS_FOR_MOMENTUM}日涨跌幅'] = ((last_price - first_price) / first_price) * 100
                 else:
                      metrics[f'近{RECENT_DAYS_FOR_MOMENTUM}日涨跌幅'] = np.nan # Avoid division by zero
            else:
                 metrics[f'近{RECENT_DAYS_FOR_MOMENTUM}日涨跌幅'] = np.nan # Not enough data or missing close price


            # --- 计算近期日均成交额 (过去 RECENT_DAYS_FOR_VOLUME 天) ---
            volume_start_date = pd.to_datetime(date.today() - timedelta(days=RECENT_DAYS_FOR_VOLUME))
            volume_data = stock_hist[stock_hist.index >= volume_start_date].copy()
            if not volume_data.empty and '成交额' in volume_data.columns:
                 metrics[f'近{RECENT_DAYS_FOR_VOLUME}日均成交额'] = volume_data['成交额'].mean()
            else:
                 metrics[f'近{RECENT_DAYS_FOR_VOLUME}日均成交额'] = np.nan # No data or missing 成交额


            # --- 计算相对强度 (过去 RELATIVE_STRENGTH_DAYS 天) ---
            metrics[f'近{RELATIVE_STRENGTH_DAYS}日相对强度'] = np.nan # Default to NaN
            if index_hist is not None:
                 rs_start_date = pd.to_datetime(date.today() - timedelta(days=RELATIVE_STRENGTH_DAYS))
                 stock_rs_data = stock_hist[stock_hist.index >= rs_start_date]
                 index_rs_data = index_hist[index_hist.index >= rs_start_date]

                 # Ensure both have enough data points in the period
                 if not stock_rs_data.empty and len(stock_rs_data) > 1 and '收盘' in stock_rs_data.columns and \
                    not index_rs_data.empty and len(index_rs_data) > 1:

                    stock_return = (stock_rs_data['收盘'].iloc[-1] / stock_rs_data['收盘'].iloc[0]) - 1
                    index_return = (index_rs_data.iloc[-1] / index_rs_data.iloc[0]) - 1

                    if index_return != 0: # Avoid division by zero
                         metrics[f'近{RELATIVE_STRENGTH_DAYS}日相对强度'] = stock_return / index_return
                    # Else: index_return is 0, relative strength is undefined, keep as NaN
                 # Else: not enough data, keep as NaN
            # Else: index data was not available, keep as NaN


            # Add this stock's metrics to the list
            metrics_list.append(metrics)

        except Exception as e:
            print(f"     处理 {stock_code} 历史数据时发生错误: {e}")
            metrics_list.append({'代码': stock_code}) # Append with potentially missing metrics


    # Convert metrics list to DataFrame and merge with selected_stocks
    if metrics_list:
        metrics_df = pd.DataFrame(metrics_list)
        # Merge on '代码', use left merge to keep all selected stocks, even if metrics calculation failed
        selected_stocks_scored = pd.merge(selected_stocks, metrics_df, on='代码', how='left')

        if not selected_stocks_scored.empty:
            print(f"\n成功计算并合并 {len(selected_stocks_scored)} 只股票的技术/市场指标。")

            # --- 应用技术/市场指标进行打分 ---
            print("\n3. 开始基于技术/市场指标打分...")

            # 示例指标和权重 (已包含你提出的涨跌停和>5%次数，权重可调整)
            tech_metrics_to_score = {
                '历史涨停次数': {'dir': 'high', 'weight': 0.40},         # 权重最高，体现股性活跃度
                '历史涨幅_gt_5_次数': {'dir': 'high', 'weight': 0.20},   # 次高权重，体现持续上涨动能
                f'近{RECENT_DAYS_FOR_MOMENTUM}日涨跌幅': {'dir': 'high', 'weight': 0.15}, # 近期动量
                f'近{RECENT_DAYS_FOR_VOLUME}日均成交额': {'dir': 'high', 'weight': 0.15}, # 近期流动性/资金关注度
                f'近{RELATIVE_STRENGTH_DAYS}日相对强度': {'dir': 'high', 'weight': 0.10}, # 相对强弱
                # 可以选择将跌停次数作为风险排除或负向指标，这里暂时不计入正向总分
                # '历史跌停次数': {'dir': 'low', 'weight': 0.0}, # 例如，可以赋0权重或负权重
            }

            # 归一化权重使其总和为1 (如果权重和不等于1)
            total_weight = sum(m['weight'] for m in tech_metrics_to_score.values())
            if total_weight > 0 and abs(total_weight - 1.0) > 1e-6: # Normalize if sum is not close to 1
                 print(f"   权重总和为 {total_weight:.2f}，进行归一化...")
                 for metric_info in tech_metrics_to_score.values():
                      metric_info['weight'] /= total_weight
            print(f"   使用的指标及归一化权重：{tech_metrics_to_score}")

            selected_stocks_scored['Total_Tech_Score'] = 0.0
            num_stocks_for_ranking = len(selected_stocks_scored)

            if num_stocks_for_ranking > 0:
                 # 对每个指标进行打分和加权求和
                 for metric, info in tech_metrics_to_score.items():
                      if metric not in selected_stocks_scored.columns:
                           print(f"   警告：打分指标列 '{metric}' 不存在于数据中，跳过该指标。")
                           continue

                      # Convert to numeric, errors='coerce' will turn non-numeric/missing values into NaN
                      temp_series = pd.to_numeric(selected_stocks_scored[metric], errors='coerce')

                      # Handle NaN and ranking: For 'high' is better metrics, NaN should result in a low score (e.g., 0).
                      # For 'low' is better metrics (not used in this positive scoring model, but for completeness), NaN depends on interpretation.
                      # Simple approach: For ranking, temporarily fill NaN. After ranking, set score to 0 if original was NaN.
                      if info['dir'] == 'high': # Higher value is better
                           # Fill NaN with a value lower than any possible actual value for ranking purposes
                           temp_series_for_rank = temp_series.fillna(temp_series.min() - 1 if pd.notna(temp_series.min()) else -999999)
                           # Rank: higher value gets lower rank number (better rank)
                           rank = temp_series_for_rank.rank(method='min', ascending=False)
                      else: # 'low' is better
                           # Fill NaN with a value higher than any possible actual value for ranking purposes
                           temp_series_for_rank = temp_series.fillna(temp_series.max() + 1 if pd.notna(temp_series.max()) else 999999)
                           # Rank: lower value gets lower rank number (better rank)
                           rank = temp_series_for_rank.rank(method='min', ascending=True)

                      # Convert rank to score (0-100 scale based on percentile ranking)
                      # Score = 100 * ( (TotalStocks - Rank) / (TotalStocks - 1) )
                      # Handle single stock case: single stock gets 100 if metric is not NaN, 0 if NaN
                      if num_stocks_for_ranking > 1:
                           score = (num_stocks_for_ranking - rank) / (num_stocks_for_ranking - 1) * 100
                           # If the original metric was NaN, explicitly set its score to 0
                           score[temp_series.isna()] = 0
                      else: # Single stock case
                           score = 100.0 if pd.notna(temp_series.iloc[0]) else 0.0


                      # Ensure scores are within 0-100 range
                      selected_stocks_scored[f'{metric}_Score'] = score.clip(lower=0, upper=100)

                      # Add to total score
                      selected_stocks_scored['Total_Tech_Score'] += selected_stocks_scored[f'{metric}_Score'] * info['weight']


                 # 4. 按技术/市场总分降序排序
                 print("\n4. 按技术/市场总分降序排序。")
                 # We will show all scored stocks, you can take .head(NUM_STOCKS_TO_SELECT) if needed
                 final_ranked_stocks = selected_stocks_scored.sort_values(by='Total_Tech_Score', ascending=False).reset_index(drop=True)


                 # 5. 显示结果
                 print("\n--- 技术/市场打分最终结果 ---")
                 if final_ranked_stocks.empty:
                     print("没有找到符合条件的股票进行技术/市场打分。")
                 else:
                     # 选取需要显示的列：股票代码, 名称, 总分, 用于打分的主要技术指标, 以及其他参考指标
                     display_cols = ['代码', '名称', 'Total_Tech_Score'] + list(tech_metrics_to_score.keys()) + ['历史跌停次数', '历史跌幅_lt_neg5_次数']

                     # Ensure all display columns exist in the DataFrame
                     display_cols = [col for col in display_cols if col in final_ranked_stocks.columns]

                     print(final_ranked_stocks[display_cols].round(2).to_string(index=True)) # Use index=True to show rank


            else:
                 print("\n未能计算到技术/市场指标，跳过打分。")

    else:
        print("\n未能计算到任何股票的技术/市场指标，跳过打分。")


print("\n--- 策略结束 ---")