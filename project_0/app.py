from flask import Flask, request, render_template, redirect, url_for, flash, jsonify, session
import pandas as pd
import akshare as ak
from datetime import date, timedelta, datetime
import numpy as np
import io # 用于从上传的文件对象读取数据
import os
import time
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import json
from config import (
    INDEX_CODE, POSITION_COUNT, NEW_STOCK_DAYS_THRESHOLD, DAILY_LIMIT_THRESHOLD,
    AMPLITUDE_DAYS, AMPLITUDE_THRESHOLD, HISTORY_DAYS_FOR_COUNT, POSITIVE_MOVE_THRESHOLD,
    RECENT_DAYS_FOR_MOMENTUM, SHORT_TERM_VOLUME_DAYS, RECENT_DAYS_FOR_VOLUME,
    RELATIVE_STRENGTH_DAYS, BENCHMARK_INDEX, PENALTY_WEIGHTS, PENALTY_WEIGHT, WEIGHTS, SIGNAL_PARAMS
)

# 添加初始资金配置
#INITIAL_CAPITAL = 60000  # 初始资金（6万）

# --- Flask 应用初始化 ---
app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024 # 最大上传文件大小 16MB
app.config['UPLOAD_EXTENSIONS'] = ['.csv'] # 允许的扩展名
app.secret_key = 'your_secret_key_here'  # 用于 flash 消息加密，实际使用时应该使用随机生成的密钥
app.config['SESSION_TYPE'] = 'filesystem'  # 让session可持久化

# 添加持仓管理相关配置
POSITION_FILE = 'positions.json'  # 持仓记录文件
REBALANCE_DAYS = 7  # 调仓周期（天）
REBALANCE_THRESHOLD = 0.1  # 调仓阈值（当股票得分变化超过10%时建议调仓）

# 添加交易记录相关配置
TRANSACTION_FILE = 'transactions.json'  # 交易记录文件

# --- 核心分析和打分逻辑函数 ---
def create_session():
    """
    创建带有重试机制的会话
    """
    session = requests.Session()
    retry_strategy = Retry(
        total=3,  # 总重试次数
        backoff_factor=1,  # 重试间隔
        status_forcelist=[500, 502, 503, 504]  # 需要重试的HTTP状态码
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

def get_stock_history_with_retry(stock_code, start_date, end_date, max_retries=3):
    """
    获取股票历史数据，带重试机制
    """
    for attempt in range(max_retries):
        try:
            # 使用akshare获取数据
            stock_hist = ak.stock_zh_a_hist(symbol=stock_code, period="daily", 
                                          start_date=start_date, end_date=end_date, 
                                          adjust="qfq")
            if stock_hist is not None and not stock_hist.empty:
                return stock_hist
        except Exception as e:
            print(f"第 {attempt + 1} 次尝试获取 {stock_code} 数据失败: {str(e)}")
            if attempt < max_retries - 1:
                # 等待一段时间后重试
                wait_time = (attempt + 1) * 2  # 递增等待时间
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                print(f"获取 {stock_code} 数据失败，已达到最大重试次数")
    return None

def get_index_history_with_retry(index_code, start_date, end_date, max_retries=3):
    """
    获取指数历史数据，带重试机制
    """
    for attempt in range(max_retries):
        try:
            print(f"第 {attempt + 1} 次尝试获取指数 {index_code} 数据...")
            # 使用akshare获取数据
            index_hist = ak.index_zh_a_hist(symbol=index_code, period="daily", 
                                          start_date=start_date, end_date=end_date)
            if index_hist is not None and not index_hist.empty:
                print(f"成功获取指数 {index_code} 数据，共 {len(index_hist)} 条记录")
                print(f"数据时间范围：{index_hist['日期'].min()} 到 {index_hist['日期'].max()}")
                # 确保数据格式正确
                if '日期' in index_hist.columns and '收盘' in index_hist.columns:
                    index_hist['日期'] = pd.to_datetime(index_hist['日期'])
                    index_hist = index_hist.set_index('日期')['收盘'].sort_index()
                    return index_hist
                else:
                    print(f"警告：指数 {index_code} 数据缺少必要列")
            else:
                print(f"警告：未获取到指数 {index_code} 数据或数据为空")
        except Exception as e:
            print(f"第 {attempt + 1} 次尝试获取指数 {index_code} 数据失败: {str(e)}")
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 2
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                print(f"获取指数 {index_code} 数据失败，已达到最大重试次数")
    return None

def calculate_penalty_score(stock_hist):
    """
    计算跌停惩罚分数，考虑时间衰减
    """
    try:
        # 获取过去一年的数据
        one_year_ago = pd.to_datetime(date.today() - timedelta(days=365))
        hist_data = stock_hist[stock_hist.index >= one_year_ago].copy()
        
        if hist_data.empty:
            return 0.0
            
        # 计算跌停日期
        limit_down_dates = hist_data[hist_data['涨跌幅'] <= -DAILY_LIMIT_THRESHOLD].index
        
        if limit_down_dates.empty:
            return 0.0
            
        # 计算时间衰减惩罚
        total_penalty = 0.0
        today = pd.to_datetime(date.today())
        
        for limit_date in limit_down_dates:
            days_diff = (today - limit_date).days
            
            if days_diff <= 90:  # 0-3个月
                weight = PENALTY_WEIGHTS['0-3个月']
            elif days_diff <= 180:  # 3-6个月
                weight = PENALTY_WEIGHTS['3-6个月']
            else:  # 6-12个月
                weight = PENALTY_WEIGHTS['6-12个月']
                
            total_penalty += weight
            
        return total_penalty
        
    except Exception as e:
        print(f"计算跌停惩罚时出错: {str(e)}")
        return 0.0

def run_scoring_strategy(selected_stocks_df: pd.DataFrame):
    """
    接收一个包含 '代码' 和 '名称' 的DataFrame，计算技术/市场指标并打分。

    Args:
        selected_stocks_df: 包含股票代码和名称的 Pandas DataFrame。

    Returns:
        一个包含打分结果的 DataFrame，如果发生错误则返回一个包含错误信息的字典。
    """
    print("--- 技术/市场打分策略开始 (Web) ---")
    print(f"处理 {len(selected_stocks_df)} 只股票...")

    if selected_stocks_df.empty:
        print("输入DataFrame为空。")
        return {"error": "输入的股票列表为空。"}

    # 确保股票代码是字符串，并补齐到6位
    if '代码' not in selected_stocks_df.columns or '名称' not in selected_stocks_df.columns:
         return {"error": "CSV文件必须包含 '代码' 和 '名称' 列。"}

    try:
         selected_stocks_df['代码'] = selected_stocks_df['代码'].astype(str).str.zfill(6)
    except Exception as e:
         return {"error": f"处理股票代码列时出错: {e}"}

    # 获取历史数据的时间范围
    end_date_str = date.today().strftime('%Y%m%d')
    max_history_days = max(HISTORY_DAYS_FOR_COUNT, RECENT_DAYS_FOR_MOMENTUM, RECENT_DAYS_FOR_VOLUME, RELATIVE_STRENGTH_DAYS) + 30
    start_date_str = (date.today() - timedelta(days=max_history_days)).strftime('%Y%m%d')

    print(f"获取数据时间范围：{start_date_str} 到 {end_date_str}")

    # 获取基准指数历史数据用于相对强度计算
    print(f"开始获取基准指数 {BENCHMARK_INDEX} 的历史数据...")
    index_hist = get_index_history_with_retry(BENCHMARK_INDEX, start_date_str, end_date_str)
    if index_hist is None:
        print("警告：无法获取基准指数数据，相对强度计算将跳过")
    else:
        print(f"成功获取基准指数数据，共 {len(index_hist)} 条记录")

    # 循环计算每个股票的指标
    metrics_list = []
    num_stocks_to_process = len(selected_stocks_df)
    print(f"开始为 {num_stocks_to_process} 只股票计算指标...")
    for i, (index, row) in enumerate(selected_stocks_df.iterrows()):
        stock_code = row['代码']
        stock_name = row['名称']

        try:
            # 获取股票历史数据
            stock_hist = get_stock_history_with_retry(stock_code, start_date_str, end_date_str)
            if stock_hist is None:
                print(f"无法获取 {stock_code} 的历史数据，跳过该股票")
                metrics_list.append({'代码': stock_code})
                continue

            if '日期' in stock_hist.columns and '收盘' in stock_hist.columns and '成交额' in stock_hist.columns and '涨跌幅' in stock_hist.columns:
                 stock_hist['日期'] = pd.to_datetime(stock_hist['日期'])
                 stock_hist = stock_hist.set_index('日期').sort_index()
            else:
                 print(f"     警告: {stock_code} 历史数据格式不正确，跳过指标计算。")
                 metrics_list.append({'代码': stock_code})
                 continue # Skip to next stock


            metrics = {'代码': stock_code}

            # --- 计算历史波动次数 (过去 HISTORY_DAYS_FOR_COUNT 天) ---
            hist_count_start_date = pd.to_datetime(date.today() - timedelta(days=HISTORY_DAYS_FOR_COUNT))
            hist_count_data = stock_hist[stock_hist.index >= hist_count_start_date].copy()
            if not hist_count_data.empty:
                 metrics['年涨停'] = (hist_count_data['涨跌幅'] >= DAILY_LIMIT_THRESHOLD).sum()
                 metrics['年跌停'] = (hist_count_data['涨跌幅'] <= -DAILY_LIMIT_THRESHOLD).sum()
                 metrics['gt_5'] = ((hist_count_data['涨跌幅'] >= POSITIVE_MOVE_THRESHOLD) & (hist_count_data['涨跌幅'] < DAILY_LIMIT_THRESHOLD)).sum()
                 metrics['lt_5'] = ((hist_count_data['涨跌幅'] <= -POSITIVE_MOVE_THRESHOLD) & (hist_count_data['涨跌幅'] > -DAILY_LIMIT_THRESHOLD)).sum()
            else:
                 metrics['年涨停'] = 0
                 metrics['年跌停'] = 0
                 metrics['gt_5'] = 0
                 metrics['lt_5'] = 0
                 print(f"     警告: {stock_code} 在过去 {HISTORY_DAYS_FOR_COUNT} 天内历史数据不足。")


            # --- 计算近期动量 (过去 RECENT_DAYS_FOR_MOMENTUM 天) ---
            momentum_start_date = pd.to_datetime(date.today() - timedelta(days=RECENT_DAYS_FOR_MOMENTUM))
            momentum_data = stock_hist[stock_hist.index >= momentum_start_date].copy()
            if not momentum_data.empty and len(momentum_data) > 1:
                 first_price = momentum_data['收盘'].iloc[0]
                 last_price = momentum_data['收盘'].iloc[-1]
                 if first_price != 0:
                      metrics[f'{RECENT_DAYS_FOR_MOMENTUM}日涨跌幅'] = ((last_price - first_price) / first_price) * 100
                 else:
                      metrics[f'{RECENT_DAYS_FOR_MOMENTUM}日涨跌幅'] = np.nan # Avoid division by zero
            else:
                 metrics[f'{RECENT_DAYS_FOR_MOMENTUM}日涨跌幅'] = np.nan # Not enough data


            # --- 计算近期成交强度 (近10日均成交额/近30日均成交额) ---
            volume_short_start_date = pd.to_datetime(date.today() - timedelta(days=SHORT_TERM_VOLUME_DAYS))
            volume_recent_start_date = pd.to_datetime(date.today() - timedelta(days=RECENT_DAYS_FOR_VOLUME))
            
            short_volume_data = stock_hist[stock_hist.index >= volume_short_start_date].copy()
            recent_volume_data = stock_hist[stock_hist.index >= volume_recent_start_date].copy()
            
            if not short_volume_data.empty and not recent_volume_data.empty:
                short_avg_volume = short_volume_data['成交额'].mean()
                recent_avg_volume = recent_volume_data['成交额'].mean()
                
                if recent_avg_volume != 0:
                    metrics['10日成交强度'] = short_avg_volume / recent_avg_volume
                else:
                    metrics['10日成交强度'] = np.nan
            else:
                metrics['10日成交强度'] = np.nan


            # --- 计算相对强度 (过去 RELATIVE_STRENGTH_DAYS 天) ---
            relative_strength_col = f'{RELATIVE_STRENGTH_DAYS}日相对强度'  # 使用基准指数名称
            metrics[relative_strength_col] = np.nan # Default to NaN
            if index_hist is not None:
                try:
                 rs_start_date = pd.to_datetime(date.today() - timedelta(days=RELATIVE_STRENGTH_DAYS))
                 stock_rs_data = stock_hist[stock_hist.index >= rs_start_date]
                 index_rs_data = index_hist[index_hist.index >= rs_start_date]

                 if not stock_rs_data.empty and len(stock_rs_data) > 1 and \
                    not index_rs_data.empty and len(index_rs_data) > 1:

                        # 计算N日收益率
                    stock_return = (stock_rs_data['收盘'].iloc[-1] / stock_rs_data['收盘'].iloc[0]) - 1
                    index_return = (index_rs_data.iloc[-1] / index_rs_data.iloc[0]) - 1

                        # 计算相对强度（股票收益率/指数收益率）
                    if index_return != 0:
                            metrics[relative_strength_col] = stock_return / index_return
                    else:
                        print("数据不足，无法计算相对强度")
                except Exception as e:
                    print(f"计算相对强度时出错: {str(e)}")

            # --- 计算振幅指标 ---
            amplitude_metrics = calculate_amplitude_metrics(stock_hist)
            metrics.update(amplitude_metrics)

            # --- 计算跌停惩罚 ---
            penalty_score = calculate_penalty_score(stock_hist)
            metrics['跌停惩罚'] = penalty_score

            # Add this stock's metrics to the list
            metrics_list.append(metrics)

        except Exception as e:
            print(f"处理 {stock_code} 时发生错误: {str(e)}")
            metrics_list.append({'代码': stock_code}) # Append with potentially missing metrics


    # Convert metrics list to DataFrame and merge
    if not metrics_list:
         return {"error": "未能为任何股票计算出技术/市场指标。"}

    metrics_df = pd.DataFrame(metrics_list)
    # Merge on '代码', use left merge to keep all selected stocks
    selected_stocks_scored = pd.merge(selected_stocks_df, metrics_df, on='代码', how='left')

    if selected_stocks_scored.empty:
        return {"error": "合并股票列表和计算的指标后，没有得到数据。"}

    # --- 应用技术/市场指标进行打分 ---
    print("\n开始基于技术/市场指标打分...")

    selected_stocks_scored['技术打分'] = 0.0
    num_stocks_for_ranking = len(selected_stocks_scored)

    if num_stocks_for_ranking > 0:
         for metric, info in WEIGHTS.items():
              if metric not in selected_stocks_scored.columns:
                   print(f"   警告：打分指标列 '{metric}' 不存在，跳过打分。")
                   continue

              temp_series = pd.to_numeric(selected_stocks_scored[metric], errors='coerce')

              if info['dir'] == 'high':
                   temp_series_for_rank = temp_series.fillna(temp_series.min() - 1 if pd.notna(temp_series.min()) else -999999)
                   rank = temp_series_for_rank.rank(method='min', ascending=False)
              else: # 'low' is better
                   temp_series_for_rank = temp_series.fillna(temp_series.max() + 1 if pd.notna(temp_series.max()) else 999999)
                   rank = temp_series_for_rank.rank(method='min', ascending=True)

              if num_stocks_for_ranking > 1:
                   score = (num_stocks_for_ranking - rank) / (num_stocks_for_ranking - 1) * 100
                   score[temp_series.isna()] = 0 # Set score to 0 if original metric was NaN
              else:
                   score = 100.0 if pd.notna(temp_series.iloc[0]) else 0.0 # Single stock

              selected_stocks_scored[f'{metric}_Score'] = score.clip(lower=0, upper=100)
              selected_stocks_scored['技术打分'] += selected_stocks_scored[f'{metric}_Score'] * info['weight']

    # 应用跌停惩罚
    selected_stocks_scored['技术打分'] = selected_stocks_scored['技术打分'] * (1 - PENALTY_WEIGHT) - selected_stocks_scored['跌停惩罚'] * PENALTY_WEIGHT * 100

    # 排序结果
    final_ranked_stocks = selected_stocks_scored.sort_values(by='技术打分', ascending=False).reset_index(drop=True)

    print("\n--- 技术/市场打分策略结束 (Web) ---")

    # 在返回结果之前，删除所有的 Score 列
    score_columns = [col for col in final_ranked_stocks.columns if col.endswith('_Score')]
    final_ranked_stocks = final_ranked_stocks.drop(columns=score_columns)

    # 设置要展示的列的顺序
    display_columns = ['代码', '名称', '技术打分', '年涨停', '年跌停', 'gt_5', 'lt_5', 
                      f'{RECENT_DAYS_FOR_MOMENTUM}日涨跌幅', '10日成交强度', f'{RELATIVE_STRENGTH_DAYS}日相对强度',
                      '平均振幅', '显著振幅天数', '最大振幅', '跌停惩罚']
    final_ranked_stocks = final_ranked_stocks[display_columns]

    # 对数值列进行格式化
    for col in final_ranked_stocks.columns:
        if col not in ['代码', '名称']:
            final_ranked_stocks[col] = final_ranked_stocks[col].round(2)

    return final_ranked_stocks

def get_file_info(file_path):
    """
    获取文件的创建时间和大小信息，强制刓新
    """
    try:
        # 清理文件状态缓存
        if hasattr(os, 'stat_float_times'):
            os.stat_float_times(False)
        
        if os.path.exists(file_path):
            # 强制刷新文件状态
            os.stat(file_path)
            # 获取文件状态信息
            stats = os.stat(file_path)
            
            # 在Windows系统上使用创建时间，在Unix系统上使用最后修改时间
            if os.name == 'nt':  # Windows
                timestamp = stats.st_mtime  # 改用修改时间而不是创建时间
            else:  # Unix
                timestamp = stats.st_mtime
            
            # 强制刷新缓存
            os.utime(file_path, (timestamp, timestamp))
            
            # 转换时间戳为datetime对象
            file_time = datetime.fromtimestamp(timestamp)
            # 格式化时间字符串
            time_str = file_time.strftime('%Y-%m-%d %H:%M:%S')
            # 获取文件大小
            size = stats.st_size
            # 格式化文件大小
            if size < 1024:
                size_str = f"{size} B"
            elif size < 1024 * 1024:
                size_str = f"{size/1024:.1f} KB"
            else:
                size_str = f"{size/(1024*1024):.1f} MB"
            
            return {
                'exists': True,
                'time': time_str,
                'size': size_str,
                'timestamp': timestamp  # 添加时间戳用于比较
            }
        else:
            return {
                'exists': False,
                'error': '文件不存在'
            }
    except Exception as e:
        return {
            'exists': False,
            'error': str(e)
        }

def calculate_amplitude_metrics(stock_hist, days=AMPLITUDE_DAYS, threshold=AMPLITUDE_THRESHOLD):
    """
    计算股票的振幅相关指标
    """
    try:
        # 获取指定天数的数据
        amplitude_data = stock_hist.tail(days)
        
        # 计算每日振幅
        daily_amplitude = (amplitude_data['最高'] - amplitude_data['最低']) / amplitude_data['收盘'].shift(1) * 100
        
        # 计算平均振幅
        avg_amplitude = daily_amplitude.mean()
        
        # 计算显著振幅（超过阈值）的天数
        significant_amplitude_days = (daily_amplitude > threshold).sum()
        
        # 计算最大振幅
        max_amplitude = daily_amplitude.max()
        
        return {
            '平均振幅': avg_amplitude,
            '显著振幅天数': significant_amplitude_days,
            '最大振幅': max_amplitude
        }
    except Exception as e:
        print(f"计算振幅指标时出错: {str(e)}")
        return {
            '平均振幅': np.nan,
            '显著振幅天数': np.nan,
            '最大振幅': np.nan
        }

# --- 持仓管理类 ---
class PositionManager:
    def __init__(self, position_file=POSITION_FILE):
        self.position_file = position_file
        self.positions = self.load_positions()
        
    def load_positions(self):
        """加载持仓记录"""
        try:
            if os.path.exists(self.position_file):
                with open(self.position_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"加载持仓记录失败: {e}")
            return {}
            
    def save_positions(self):
        """保存持仓记录"""
        try:
            # 使用临时文件进行写入
            temp_file = self.position_file + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(self.positions, f, ensure_ascii=False, indent=4)
                f.flush()
                os.fsync(f.fileno())  # 确保写入磁盘
            
            # 在 Windows 上使用 replace 来确保原子性
            if os.path.exists(self.position_file):
                os.remove(self.position_file)
            os.rename(temp_file, self.position_file)
            
            # 强制刷新文件系统缓存
            if hasattr(os, 'sync'):
                os.sync()
        except Exception as e:
            print(f"保存持仓记录失败: {e}")
            if os.path.exists(temp_file):
                os.remove(temp_file)
            
    def update_position(self, stock_code, stock_name, cost_price, amount, buy_date):
        """更新持仓信息并记录交易"""
        # 如果是新持仓，记录买入交易
        if stock_code not in self.positions:
            transaction_manager.add_transaction(
                stock_code,
                stock_name,
                'buy',
                cost_price,
                amount,
                buy_date
            )
        
        self.positions[stock_code] = {
            'name': stock_name,
            'cost_price': cost_price,
            'amount': amount,
            'buy_date': buy_date,
            'last_update': datetime.now().strftime('%Y-%m-%d')
        }
        self.save_positions()
        
    def remove_position(self, stock_code):
        """移除持仓并记录交易"""
        if stock_code in self.positions:
            position = self.positions[stock_code]
            current_price = get_current_price(stock_code)
            
            if current_price is not None:
                # 记录卖出交易
                transaction_manager.add_transaction(
                    stock_code,
                    position['name'],
                    'sell',
                    current_price,
                    position['amount']
                )
            
            del self.positions[stock_code]
            self.save_positions()
            
    def get_position_info(self, stock_code, current_price):
        """获取持仓信息"""
        if stock_code not in self.positions:
            return None
            
        position = self.positions[stock_code]
        cost_price = position['cost_price']
        amount = position['amount']
        current_value = current_price * amount
        cost_value = cost_price * amount
        profit = current_value - cost_value
        profit_pct = (current_price / cost_price - 1) * 100
        
        return {
            'name': position['name'],
            'cost_price': cost_price,
            'current_price': current_price,
            'amount': amount,
            'current_value': current_value,
            'cost_value': cost_value,
            'profit': profit,
            'profit_pct': profit_pct,
            'buy_date': position['buy_date'],
            'holding_days': (datetime.now() - datetime.strptime(position['buy_date'], '%Y-%m-%d')).days
        }

# 创建持仓管理器实例
position_manager = PositionManager()

# --- 交易记录管理类 ---
class TransactionManager:
    def __init__(self, transaction_file=TRANSACTION_FILE):
        self.transaction_file = transaction_file
        self.transactions = self.load_transactions()
        
    def load_transactions(self):
        """加载交易记录"""
        try:
            if os.path.exists(self.transaction_file):
                with open(self.transaction_file, 'r', encoding='utf-8') as f:
                    transactions = json.load(f)
            else:
                transactions = []
                
            # 补全历史持仓的买入记录
            current_positions = position_manager.positions
            existing_codes = {trans['code'] for trans in transactions}
            
            # 检查每个当前持仓，如果没有对应的买入记录，则添加
            for code, pos in current_positions.items():
                if code not in existing_codes:
                    # 添加买入记录
                    transactions.append({
                        'code': code,
                        'name': pos['name'],
                        'action': 'buy',
                        'price': pos['cost_price'],
                        'amount': pos['amount'],
                        'value': pos['cost_price'] * pos['amount'],
                        'date': pos['buy_date']
                    })
            
            # 按日期排序
            transactions.sort(key=lambda x: x['date'])
            
            # 保存更新后的交易记录
            if len(transactions) > 0:
                self.save_transactions(transactions)
                
            return transactions
        except Exception as e:
            print(f"加载交易记录失败: {e}")
            return []
            
    def save_transactions(self, transactions=None):
        """保存交易记录"""
        try:
            if transactions is None:
                transactions = self.transactions
                
            # 使用临时文件进行写入
            temp_file = self.transaction_file + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(transactions, f, ensure_ascii=False, indent=4)
                f.flush()
                os.fsync(f.fileno())
            
            if os.path.exists(self.transaction_file):
                os.remove(self.transaction_file)
            os.rename(temp_file, self.transaction_file)
            
            if hasattr(os, 'sync'):
                os.sync()
        except Exception as e:
            print(f"保存交易记录失败: {e}")
            if os.path.exists(temp_file):
                os.remove(temp_file)
                
    def add_transaction(self, code, name, action, price, amount, date=None):
        """添加交易记录"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
            
        transaction = {
            'code': code,
            'name': name,
            'action': action,  # 'buy' 或 'sell'
            'price': price,
            'amount': amount,
            'value': price * amount,
            'date': date
        }
        
        self.transactions.append(transaction)
        self.save_transactions()
        
    def get_transaction_summary(self):
        """获取交易统计信息"""
        realized_profit = 0.0   # 已实现盈亏
        unrealized_profit = 0.0 # 未实现盈亏
        current_value = 0.0     # 当前市值
        total_investment = 0.0  # 历史总投资金额
        total_withdrawal = 0.0  # 历史总卖出金额

        # 计算当前持仓的市值
        for code, pos in position_manager.positions.items():
            current_price = get_current_price(code)
            if current_price is not None:
                current_value += current_price * pos['amount']

        # 计算已实现盈亏和投资统计
        stock_trades = {}  # 用于跟踪每只股票的交易
        for trans in sorted(self.transactions, key=lambda x: x['date']):  # 按日期排序
            code = trans['code']
            if code not in stock_trades:
                stock_trades[code] = {
                    'total_cost': 0.0,    # 总成本
                    'total_amount': 0,    # 总数量
                    'realized_profit': 0.0 # 已实现盈亏
                }

            trade = stock_trades[code]
            if trans['action'] == 'buy':
                # 买入时更新成本和总投资
                trade['total_cost'] += trans['value']
                trade['total_amount'] += trans['amount']
                total_investment += trans['value']
            else:  # sell
                total_withdrawal += trans['value']
                if trade['total_amount'] > 0:
                    # 计算卖出时的成本价
                    cost_per_share = trade['total_cost'] / trade['total_amount']
                    # 计算这次卖出的成本
                    sell_cost = cost_per_share * trans['amount']
                    # 计算这次卖出的盈亏
                    profit = trans['value'] - sell_cost
                    trade['realized_profit'] += profit
                    # 更新剩余持仓的成本
                    trade['total_cost'] = cost_per_share * (trade['total_amount'] - trans['amount'])
                    trade['total_amount'] -= trans['amount']

        # 汇总所有股票的已实现盈亏
        realized_profit = sum(trade['realized_profit'] for trade in stock_trades.values())

        # 计算未实现盈亏（当前市值 - 当前持仓成本）
        current_cost = sum(trade['total_cost'] for trade in stock_trades.values())
        unrealized_profit = current_value - current_cost

        # 计算总盈亏
        total_profit = realized_profit + unrealized_profit

        # 计算净投资金额（投入 - 取出）
        net_investment = total_investment - total_withdrawal

        # 计算总盈亏比例的正确方法：
        # 总盈亏比例 = (当前市值 + 已取出金额 - 总投入金额) / 总投入金额
        # 或者简化为：总盈亏 / 总投入金额
        if total_investment > 0:
            total_profit_pct = (total_profit / total_investment) * 100
        else:
            total_profit_pct = 0.0

        # 当前持仓收益率（仅针对当前持有的股票）
        current_profit_pct = (unrealized_profit / current_cost * 100) if current_cost > 0 else 0.0

        print(f"Debug - 历史总投资: {total_investment:.2f}")
        print(f"Debug - 历史总卖出: {total_withdrawal:.2f}")
        print(f"Debug - 净投资: {net_investment:.2f}")
        print(f"Debug - 已实现盈亏: {realized_profit:.2f}")
        print(f"Debug - 未实现盈亏: {unrealized_profit:.2f}")
        print(f"Debug - 总盈亏: {total_profit:.2f}")
        print(f"Debug - 当前市值: {current_value:.2f}")
        print(f"Debug - 当前成本: {current_cost:.2f}")
        print(f"Debug - 总盈亏比例: {total_profit_pct:.2f}%")
        print(f"Debug - 当前持仓收益率: {current_profit_pct:.2f}%")

        return {
            'current_value': current_value,
            'realized_profit': realized_profit,
            'unrealized_profit': unrealized_profit,
            'total_profit': total_profit,
            'total_profit_pct': total_profit_pct,
            'current_profit_pct': current_profit_pct,
            'total_investment': total_investment,
            'net_investment': net_investment
        }

# 创建交易记录管理器实例
transaction_manager = TransactionManager()

# --- 调仓建议类 ---
class RebalanceAdvisor:
    def __init__(self, position_manager):
        self.position_manager = position_manager
        
    def get_rebalance_suggestions(self, scored_stocks):
        """获取调仓建议"""
        suggestions = []
        current_positions = self.position_manager.positions
        
        # 获取当前持仓股票的得分
        for stock_code in current_positions:
            stock_info = scored_stocks[scored_stocks['代码'] == stock_code]
            if not stock_info.empty:
                current_score = stock_info['技术打分'].iloc[0]
                # 获取当前价格（使用最近一个交易日的收盘价）
                try:
                    stock_hist = get_stock_history_with_retry(stock_code, 
                                                            (datetime.now() - timedelta(days=1)).strftime('%Y%m%d'),
                                                            datetime.now().strftime('%Y%m%d'))
                    if stock_hist is not None and not stock_hist.empty:
                        current_price = stock_hist['收盘'].iloc[-1]
                        position_info = self.position_manager.get_position_info(stock_code, current_price)
                        
                        # 检查是否需要调仓
                        if position_info['profit_pct'] < -10:  # 亏损超过10%
                            suggestions.append({
                                'code': stock_code,
                                'name': position_info['name'],
                                'action': '卖出',
                                'reason': f'亏损超过10%（当前亏损{position_info["profit_pct"]:.2f}%）',
                                'current_score': current_score
                            })
                except Exception as e:
                    print(f"获取股票 {stock_code} 价格失败: {e}")
        
        # 检查是否有更好的替代股票
        top_stocks = scored_stocks.head(20)  # 获取得分最高的20只股票
        for _, stock in top_stocks.iterrows():
            if stock['代码'] not in current_positions:
                suggestions.append({
                    'code': stock['代码'],
                    'name': stock['名称'],
                    'action': '买入',
                    'reason': f'技术得分较高（{stock["技术打分"]:.2f}分）',
                    'current_score': stock['技术打分']
                })
        
        return suggestions

# 创建调仓建议器实例
rebalance_advisor = RebalanceAdvisor(position_manager)

# --- Flask Routes ---

@app.route('/')
def index():
    """
    渲染文件上传页面。
    """
    # 获取时间戳参数
    ts = request.args.get('_t')
    
    # 获取 selected_stocks.csv 的文件信息
    file_info = get_file_info('selected_stocks.csv')
    
    # 添加当前时间戳到文件信息中
    if file_info.get('exists', False):
        file_info['current_ts'] = int(time.time())
    
    return render_template('index.html', 
                         file_info=file_info)

@app.route('/positions')
def positions():
    """渲染持仓管理页面"""
    # 强制重新加载持仓数据
    position_manager.positions = position_manager.load_positions()
    
    # 获取持仓信息
    positions = []
    for code, pos in position_manager.positions.items():
        try:
            stock_hist = get_stock_history_with_retry(code, 
                                                    (datetime.now() - timedelta(days=7)).strftime('%Y%m%d'),
                                                    datetime.now().strftime('%Y%m%d'))
            if stock_hist is not None and not stock_hist.empty:
                stock_hist = stock_hist.sort_values('日期')
                last_row = stock_hist.iloc[-1]
                current_price = last_row['收盘']
                info = position_manager.get_position_info(code, current_price)
                if info:
                    positions.append({
                        'code': code,
                        'name': pos['name'],
                        'amount': pos['amount'],
                        'cost_price': pos['cost_price'],
                        'current_price': current_price,
                        'current_value': info['current_value'],
                        'profit': info['profit'],
                        'profit_pct': info['profit_pct'],
                        'holding_days': info['holding_days'],
                        'buy_date': pos['buy_date']
                    })
        except Exception as e:
            print(f"获取股票 {code} 价格失败: {e}")
    
    # 获取交易统计信息
    summary = transaction_manager.get_transaction_summary()
    
    return render_template('positions.html', 
                         positions=positions,
                         total_value=summary['current_value'],
                         total_profit=summary['total_profit'],
                         total_profit_pct=summary['total_profit_pct'],
                         realized_profit=summary['realized_profit'],
                         unrealized_profit=summary['unrealized_profit'])

@app.route('/upload_and_score', methods=['POST'])
def upload_and_score():
    """
    接收上传的 CSV 文件，运行打分策略，并显示结果。
    如果没有上传文件，则默认读取 selected_stocks.csv
    """
    if 'csv_file' not in request.files or request.files['csv_file'].filename == '':
        # 默认读取 selected_stocks.csv
        try:
            selected_stocks_df = pd.read_csv('selected_stocks.csv')
        except Exception as e:
            return render_template('results.html', error=f"读取默认文件 selected_stocks.csv 失败: {str(e)}")
    else:
        file = request.files['csv_file']
        if not file.filename.endswith('.csv'):
            return render_template('results.html', error="请上传 CSV 文件。")
        try:
            # 从上传的文件对象读取数据
            selected_stocks_df = pd.read_csv(io.StringIO(file.read().decode('utf-8')))
        except Exception as e:
            return render_template('results.html', error=f"读取上传的 CSV 文件失败: {str(e)}")

    # 运行打分策略
    result = run_scoring_strategy(selected_stocks_df)
    if isinstance(result, dict) and 'error' in result:
        return render_template('results.html', error=result['error'])

    # 将结果存入session
    session['table_data'] = result.to_dict('records')
    session['RECENT_DAYS_FOR_MOMENTUM'] = RECENT_DAYS_FOR_MOMENTUM
    session['RELATIVE_STRENGTH_DAYS'] = RELATIVE_STRENGTH_DAYS
    session['BENCHMARK_INDEX'] = BENCHMARK_INDEX
    session['positions'] = list(position_manager.positions.keys())
    session['POSITION_COUNT'] = POSITION_COUNT

    # 分析后重定向到结果页
    return redirect(url_for('results'))

@app.route('/results')
def results():
    """
    只展示session中的分析结果，不做任何计算。
    """
    table_data = session.get('table_data')
    RECENT_DAYS_FOR_MOMENTUM = session.get('RECENT_DAYS_FOR_MOMENTUM')
    RELATIVE_STRENGTH_DAYS = session.get('RELATIVE_STRENGTH_DAYS')
    BENCHMARK_INDEX = session.get('BENCHMARK_INDEX')
    positions = session.get('positions')
    POSITION_COUNT = session.get('POSITION_COUNT', 6)
    if not table_data:
        return render_template('results.html', error='请先上传股票列表并分析，才能查看结果。')
    # 只取前N*2个候选
    if table_data:
        table_data = table_data[:POSITION_COUNT*2]
    return render_template('results.html',
                         table_data=table_data,
                         RECENT_DAYS_FOR_MOMENTUM=RECENT_DAYS_FOR_MOMENTUM,
                         RELATIVE_STRENGTH_DAYS=RELATIVE_STRENGTH_DAYS,
                         BENCHMARK_INDEX=BENCHMARK_INDEX,
                         positions=positions,
                         POSITION_COUNT=POSITION_COUNT)

@app.route('/regenerate_stocks', methods=['GET', 'POST'])
def regenerate_stocks():
    """
    重新生成股票列表
    """
    try:
        import subprocess
        result = subprocess.run(['python', 'xsz_akshare.py'], 
                              capture_output=True, 
                              text=True, 
                              encoding='utf-8')
        
        if result.returncode == 0:
            # 尝试读取生成的文件来验证
            try:
                df = pd.read_csv('selected_stocks.csv')
                stock_count = len(df)
                # 获取最新的文件信息
                file_info = get_file_info('selected_stocks.csv')
                flash(f'股票列表已成功更新！共生成 {stock_count} 只股票。', 'success')
                # 使用查询参数强制刷新
                return redirect(url_for('index', _t=int(time.time())))
            except Exception as e:
                flash(f'股票列表生成可能成功，但无法读取文件：{str(e)}', 'warning')
                return redirect(url_for('index', _t=int(time.time())))
        else:
            error_msg = f"生成股票列表失败: {result.stderr}"
            flash(error_msg, 'error')
            return redirect(url_for('index', _t=int(time.time())))
            
    except Exception as e:
        error_msg = f"执行脚本出错: {str(e)}"
        flash(error_msg, 'error')
        return redirect(url_for('index', _t=int(time.time())))

# 添加持仓操作路由
@app.route('/position/update', methods=['POST'])
def update_position():
    data = request.get_json()
    code = data.get('code')
    name = data.get('name')
    cost_price = data.get('cost_price')
    amount = data.get('amount')
    
    if not all([code, name, cost_price, amount]):
        return jsonify({'status': 'error', 'message': '所有字段不能为空'})
    
    try:
        position_manager.update_position(
            code,
            name,
            float(cost_price),
            int(amount),
            datetime.now().strftime('%Y-%m-%d')
        )
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/position/remove', methods=['POST'])
def remove_position():
    data = request.get_json()
    code = data.get('code')
    if not code:
        return jsonify({'status': 'error', 'message': '股票代码不能为空'})
    
    try:
        position_manager.remove_position(code)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/position/edit', methods=['POST'])
def edit_position():
    data = request.get_json()
    code = data.get('code')
    name = data.get('name')
    cost_price = data.get('cost_price')
    buy_date = data.get('buy_date')
    amount = data.get('amount')
    
    if not all([code, name, cost_price, buy_date, amount]):
        return jsonify({'status': 'error', 'message': '所有字段不能为空'})
    
    try:
        position_manager.update_position(
            code,
            name,
            float(cost_price),
            int(amount),
            buy_date
        )
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

def get_current_price(stock_code):
    """获取股票当前价格"""
    try:
        print(f"Debug - 开始获取股票 {stock_code} 的当前价格")
        stock_hist = get_stock_history_with_retry(stock_code,
                                                (datetime.now() - timedelta(days=1)).strftime('%Y%m%d'),
                                                datetime.now().strftime('%Y%m%d'))
        if stock_hist is not None and not stock_hist.empty:
            print(f"Debug - 成功获取股票 {stock_code} 的历史数据，最新收盘价: {stock_hist['收盘'].iloc[-1]}")
            return stock_hist['收盘'].iloc[-1]
        else:
            print(f"Debug - 获取股票 {stock_code} 的历史数据失败或数据为空")
    except Exception as e:
        print(f"Debug - 获取股票 {stock_code} 价格时发生错误: {str(e)}")
    return None

def get_stock_name(stock_code):
    """获取股票名称"""
    try:
        # 首先尝试从session中的table_data获取
        table_data = session.get('table_data', [])
        for stock in table_data:
            if stock.get('代码') == stock_code:
                return stock.get('名称', stock_code)

        # 如果session中没有，尝试从akshare获取
        stock_info = ak.stock_individual_info_em(symbol=stock_code)
        if stock_info is not None and not stock_info.empty:
            # 查找股票名称
            name_row = stock_info[stock_info['item'] == '股票名称']
            if not name_row.empty:
                return name_row['value'].iloc[0]
    except Exception as e:
        print(f"获取股票 {stock_code} 名称时出错: {str(e)}")

    # 如果都获取不到，返回股票代码
    return stock_code

def check_stop_loss(position_info):
    """
    检查是否触发止损
    使用配置文件中的止损阈值（相对成本价的比例）
    当前价格低于成本价的阈值时触发止损
    """
    stoploss_threshold = SIGNAL_PARAMS['INDIVIDUAL_STOPLOSS']
    current_ratio = 1 + position_info['profit_pct']/100  # 将百分比转换为比例
    if current_ratio <= stoploss_threshold:
        return True, f"触发止损：当前价格为成本价的{current_ratio:.2%}（阈值{stoploss_threshold:.2%}）"
    return False, None

def check_limit_up(stock_code):
    """检查是否涨停或涨停破板"""
    try:
        # 获取最近两天的数据
        stock_hist = get_stock_history_with_retry(stock_code, 
                                                (datetime.now() - timedelta(days=2)).strftime('%Y%m%d'),
                                                datetime.now().strftime('%Y%m%d'))
        if stock_hist is not None and not stock_hist.empty:
            # 检查是否涨停
            last_row = stock_hist.iloc[-1]
            if last_row['涨跌幅'] >= 9.9:  # 涨停
                return True, "涨停"
            
            # 检查是否涨停破板
            if len(stock_hist) > 1:
                prev_row = stock_hist.iloc[-2]
                # 如果昨天涨停
                if prev_row['涨跌幅'] >= 9.9:
                    # 计算昨天的涨停价
                    yesterday_limit_up_price = prev_row['收盘']  # 昨天收盘价就是涨停价
                    # 如果今天价格跌破昨天涨停价
                    if last_row['最低'] < yesterday_limit_up_price:
                        return True, "涨停破板"
    except Exception as e:
        print(f"检查涨停状态失败: {e}")
    return False, None

def check_high_volume(stock_code):
    """检查是否放量"""
    try:
        # 获取最近120天的数据
        stock_hist = get_stock_history_with_retry(stock_code, 
                                                (datetime.now() - timedelta(days=120)).strftime('%Y%m%d'),
                                                datetime.now().strftime('%Y%m%d'))
        if stock_hist is not None and not stock_hist.empty:
            # 计算最近一天的成交量是否超过过去120天最高成交量的90%
            max_volume = stock_hist['成交额'].max()
            last_volume = stock_hist['成交额'].iloc[-1]
            if last_volume > max_volume * 0.9:
                return True, f"成交量异常（{last_volume/max_volume:.1%}）"
    except Exception as e:
        print(f"检查成交量失败: {str(e)}")
    return False, None

def is_rebalance_day():
    """检查今天是否是调仓日（周二）"""
    return datetime.now().weekday() == 1  # 1 表示周二

@app.route('/rebalance_suggestions')
def get_rebalance_suggestions():
    try:
        # 检查是否是调仓日
        if not is_rebalance_day():
            return jsonify([{
                'type': 'system',
                'message': '今天不是调仓日（每周二）'
            }])
        
        # 从session获取评分数据
        table_data = session.get('table_data')
        if not table_data:
            return jsonify([{
                'type': 'system',
                'message': '请先运行评分策略'
            }])
        
        # 将session数据转换回DataFrame
        df = pd.DataFrame(table_data)
        print("DEBUG - DataFrame列名:", df.columns.tolist())
        print("DEBUG - 技术打分列是否存在:", '技术打分' in df.columns)
        
        # 确保技术打分列存在且为数值类型
        if '技术打分' not in df.columns:
            return jsonify([{
                'type': 'error',
                'message': '评分数据中缺少技术打分列'
            }])
            
        df['技术打分'] = pd.to_numeric(df['技术打分'], errors='coerce')
        print("DEBUG - 技术打分的非空值数量:", df['技术打分'].notna().sum())
        
        # 获取候选股票列表
        candidate_stocks = set(df['代码'].tolist())
        
        # 获取当前持仓
        positions = position_manager.positions
        
        # 获取前10名和前6名股票
        top_ten = df.nlargest(10, '技术打分')
        top_six = df.nlargest(6, '技术打分')
        top_ten_codes = set(top_ten['代码'].tolist())
        top_six_codes = set(top_six['代码'].tolist())
        
        try:
            # 计算所有股票的排名
            df['排名'] = df['技术打分'].rank(ascending=False, method='min')
            # 将排名转换为整数，但保留NaN值
            df['排名'] = df['排名'].fillna(-1).astype(int)
            df.loc[df['排名'] == -1, '排名'] = None
            print("DEBUG - 排名列创建成功，示例值:", df['排名'].head().tolist())
        except Exception as e:
            print("DEBUG - 创建排名列时出错:", str(e))
            df['排名'] = None
        
        suggestions = []
        
        # 添加系统消息
        suggestions.append({
            'type': 'system',
            'message': '今天是调仓日（周二），以下是调仓建议：'
        })
        
        # 处理当前持仓
        for code, pos in positions.items():
            name = pos['name']
            cost_price = pos['cost_price']
            
            # 获取当前价格
            current_price = get_current_price(code)
            if current_price is None:
                continue
                
            profit_pct = (current_price / cost_price - 1) * 100
            
            # 获取股票的排名和评分
            stock_info = df[df['代码'] == code]
            current_rank = None
            current_score = None
            
            if not stock_info.empty:
                if '排名' in stock_info.columns:
                    current_rank = stock_info['排名'].iloc[0]
                if '技术打分' in stock_info.columns:
                    current_score = stock_info['技术打分'].iloc[0]
            
            rank_info = ""
            if current_rank is not None and current_score is not None:
                rank_info = f"（当前排名：{current_rank}，得分：{current_score:.2f}）"
            
            # 检查是否在候选名单中
            if code not in candidate_stocks:
                suggestions.append({
                    'code': code,
                    'name': name,
                    'action': '卖出',
                    'reason': f'不在候选名单中{rank_info}',
                    'current_price': current_price,
                    'cost_price': cost_price,
                    'profit_pct': profit_pct,
                    'type': 'not_in_list'
                })
            # 检查是否跌出前10名
            elif code not in top_ten_codes:
                suggestions.append({
                    'code': code,
                    'name': name,
                    'action': '卖出',
                    'reason': f'跌出候选名单前10名{rank_info}',
                    'current_price': current_price,
                    'cost_price': cost_price,
                    'profit_pct': profit_pct,
                    'type': 'rank_down'
                })
            # 检查是否跌出前6名
            elif code not in top_six_codes:
                suggestions.append({
                    'code': code,
                    'name': name,
                    'action': '持有',
                    'reason': f'跌出前6名{rank_info}',
                    'current_price': current_price,
                    'cost_price': cost_price,
                    'profit_pct': profit_pct,
                    'type': 'rank_warning'
                })
            
            # 检查止损条件
            if profit_pct < -10:
                suggestions.append({
                    'code': code,
                    'name': name,
                    'action': '卖出',
                    'reason': '亏损超过10%',
                    'current_price': current_price,
                    'cost_price': cost_price,
                    'profit_pct': profit_pct,
                    'type': 'stop_loss'
                })
            
            # 检查涨停条件
            is_limit_up, limit_up_reason = check_limit_up(code)
            if is_limit_up:
                suggestions.append({
                    'code': code,
                    'name': name,
                    'action': '持有',
                    'reason': limit_up_reason,
                    'current_price': current_price,
                    'cost_price': cost_price,
                    'profit_pct': profit_pct,
                    'type': 'limit_up'
                })
        
        # 处理未持仓的前6名股票
        for _, row in top_six.iterrows():
            code = row['代码']
            name = row['名称']
            score = row['技术打分']
            rank = row['排名'] if '排名' in row else None
            
            rank_info = f"（排名：{rank}，得分：{score:.2f}）" if rank is not None else f"（得分：{score:.2f}）"
            
            # 如果不在当前持仓中，建议买入
            if code not in positions:
                current_price = get_current_price(code)
                if current_price is not None:
                    suggestions.append({
                        'code': code,
                        'name': name,
                        'action': '买入',
                        'reason': f'进入候选名单前6名{rank_info}',
                        'current_price': current_price,
                        'type': 'buy'
                    })
        
        return jsonify(suggestions)
        
    except Exception as e:
        print("DEBUG - 生成调仓建议时出错:", str(e))
        return jsonify([{
            'type': 'error',
            'message': f'生成调仓建议时出错：{str(e)}'
        }])

# 添加交易记录API
@app.route('/transactions')
def get_transactions():
    """获取交易记录"""
    return jsonify(transaction_manager.transactions)

@app.route('/api/position/<stock_code>', methods=['GET'])
def get_position_info_api(stock_code):
    """获取单个持仓信息"""
    try:
        position = position_manager.get_position_info(stock_code, get_current_price(stock_code))
        if position:
            return jsonify({
                'success': True,
                'cost_price': position.get('cost_price'),
                'quantity': position.get('amount'),
                'notes': position.get('notes', '')
            })
        return jsonify({
            'success': False,
            'message': '未找到该持仓信息'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@app.route('/api/position/update/<stock_code>', methods=['POST'])
def update_position_api(stock_code):
    """更新持仓信息"""
    try:
        data = request.get_json()
        # 获取现有持仓信息以获取股票名称
        existing_position = position_manager.positions.get(stock_code)
        if not existing_position:
            return jsonify({
                'success': False,
                'message': '持仓不存在'
            })

        # 更新持仓信息
        position_manager.update_position(
            stock_code,
            existing_position['name'],  # 使用现有的股票名称
            float(data.get('cost_price')),
            int(data.get('quantity')),
            data.get('buy_date', existing_position['buy_date'])  # 如果没有提供买入日期，使用现有的
        )
        return jsonify({
            'success': True,
            'message': '更新成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@app.route('/api/execute_action', methods=['POST'])
def execute_action():
    """执行买入或卖出操作"""
    try:
        data = request.get_json()
        stock_code = data.get('code')
        action = data.get('action')

        if action == '买入':
            # 执行买入逻辑
            # 获取股票当前价格作为买入价格
            current_price = get_current_price(stock_code)
            if current_price is None:
                return jsonify({
                    'success': False,
                    'message': '无法获取股票当前价格，买入失败'
                })

            # 获取股票名称（从调仓建议或其他地方获取）
            stock_name = get_stock_name(stock_code)
            if not stock_name:
                stock_name = stock_code  # 如果获取不到名称，使用代码作为名称

            # 获取买入数量，如果没有提供则使用默认值
            amount = data.get('amount', 100)  # 默认买入100股

            # 检查是否已经持有该股票
            if stock_code in position_manager.positions:
                # 如果已经持有，更新持仓（增加数量，重新计算平均成本）
                existing_pos = position_manager.positions[stock_code]
                existing_amount = existing_pos['amount']
                existing_cost = existing_pos['cost_price']

                # 计算新的平均成本价
                total_cost = existing_amount * existing_cost + amount * current_price
                new_amount = existing_amount + amount
                new_avg_cost = total_cost / new_amount

                position_manager.update_position(
                    stock_code,
                    stock_name,
                    new_avg_cost,
                    new_amount,
                    existing_pos['buy_date']  # 保持原买入日期
                )

                return jsonify({
                    'success': True,
                    'message': f'成功加仓 {stock_name}({stock_code})，买入价格：{current_price:.2f}，数量：{amount}股，新平均成本：{new_avg_cost:.2f}'
                })
            else:
                # 新建持仓
                position_manager.update_position(
                    stock_code,
                    stock_name,
                    current_price,
                    amount,
                    datetime.now().strftime('%Y-%m-%d')
                )

                return jsonify({
                    'success': True,
                    'message': f'成功买入 {stock_name}({stock_code})，价格：{current_price:.2f}，数量：{amount}股'
                })

        elif action == '卖出':
            # 执行卖出逻辑
            if stock_code not in position_manager.positions:
                return jsonify({
                    'success': False,
                    'message': '该股票不在持仓中，无法卖出'
                })

            # 移除持仓（这会自动记录卖出交易）
            position_manager.remove_position(stock_code)

            return jsonify({
                'success': True,
                'message': f'成功卖出 {stock_code}'
            })

        return jsonify({
            'success': False,
            'message': '未知操作类型'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

def update_position_info(stock_code, data):
    """更新持仓信息的辅助函数"""
    # TODO: 实现实际的数据存储逻辑
    pass

def get_position_info(stock_code):
    """获取持仓信息的辅助函数"""
    # TODO: 实现实际的数据获取逻辑
    pass

# --- 运行 Flask 应用 ---
if __name__ == '__main__':
    # 使用 debug=True 可以看到更详细的错误信息，但在生产环境中应关闭
    #app.run(debug=True)
    app.run(host='0.0.0.0', port=5000, debug=False) # 生产环境配置示例
