import akshare as ak
import pandas as pd
from datetime import datetime, date, timedelta
from config import INDEX_CODE, POSITION_COUNT, NEW_STOCK_DAYS_THRESHOLD, DAILY_LIMIT_THRESHOLD
NUM_STOCKS_TO_SELECT = POSITION_COUNT * 2

print("--- 股票筛选策略开始 ---")
print(f"目标指数：中小板指 ({INDEX_CODE})")
print(f"持仓数：{POSITION_COUNT} 只，候选数量：{NUM_STOCKS_TO_SELECT} 只")
print(f"次新股定义：上市不足 {NEW_STOCK_DAYS_THRESHOLD} 天")
# 修改了涨跌停判断的描述，因为现在用涨跌额和昨收计算涨跌幅
print(f"涨跌停判断：计算涨跌幅 >= {DAILY_LIMIT_THRESHOLD}% 或 <= -{DAILY_LIMIT_THRESHOLD}%")
print("-" * 20)

# 1. 获取中小板指数成分股列表
print(f"1. 获取指数 {INDEX_CODE} 的成分股列表...")
try:
    index_constituents = ak.index_stock_cons(symbol=INDEX_CODE)
    if index_constituents.empty:
        print(f"错误：未获取到指数 {INDEX_CODE} 的成分股数据。")
        #exit()
    index_codes = index_constituents['品种代码'].tolist()
    print(f"   获取到 {len(index_codes)} 只成分股。")
except Exception as e:
    print(f"获取指数成分股失败: {e}")
    #exit()

# 2. 获取A股市场所有股票的实时行情数据
#    这包含市值、涨跌额、昨收、成交量等信息，用于后续过滤
print("2. 获取A股市场所有股票的实时行情数据...")
try:
    # 确保获取涨跌额、昨收、成交量和总市值这些关键字段
    stock_spot_all = ak.stock_zh_a_spot_em()
    if stock_spot_all.empty:
        print("错误：未获取到A股所有股票的实时行情数据。")
        exit()

    # 确保数据类型正确并处理NaN
    stock_spot_all['总市值'] = pd.to_numeric(stock_spot_all['总市值'], errors='coerce')
    stock_spot_all['成交量'] = pd.to_numeric(stock_spot_all['成交量'], errors='coerce')
    stock_spot_all['涨跌额'] = pd.to_numeric(stock_spot_all['涨跌额'], errors='coerce')
    stock_spot_all['昨收'] = pd.to_numeric(stock_spot_all['昨收'], errors='coerce')

    # 移除市值、成交量、涨跌额或昨收为NaN的行，这些数据是过滤的关键
    stock_spot_all.dropna(subset=['总市值', '成交量', '涨跌额', '昨收'], inplace=True)

    print(f"   获取到 {len(stock_spot_all)} 只股票的实时行情数据。")
except Exception as e:
    print(f"获取实时行情数据失败: {e}")
    #exit()

# 3. 将指数成分股与实时行情数据合并
#    只保留既是成分股又有实时数据的股票
print("3. 合并指数成分股与实时行情数据...")
# 使用 inner merge 确保只保留在 index_codes 列表中的股票
filtered_stocks = pd.merge(
    stock_spot_all,
    pd.DataFrame({'代码': index_codes}),
    on='代码',
    how='inner'
).copy()

if filtered_stocks.empty:
    print("错误：合并后没有找到匹配的股票数据。")
    exit()
print(f"   合并后剩余 {len(filtered_stocks)} 只股票。")

# 4. 获取A股市场所有股票的基础信息（包含上市日期）
#    用于判断次新股
print("4. 获取A股市场所有股票的基础信息（上市日期）...")
try:
    stock_info_all = ak.stock_xgsr_ths()
    if stock_info_all.empty:
        print("错误：未获取到A股所有股票的基础信息。")
        #exit()
    stock_info_all['上市日期'] = pd.to_datetime(stock_info_all['上市日期'], errors='coerce')
    stock_info_all.dropna(subset=['上市日期'], inplace=True) # 移除无法转换的数据
    stock_info_all = stock_info_all[['股票代码', '上市日期']] # 只保留需要的信息
    stock_info_all.rename(columns={'股票代码': '代码'}, inplace=True)
    print(f"   获取到 {len(stock_info_all)} 只股票的基础信息。")
except Exception as e:
    print(f"获取股票基础信息失败: {e}")
    #exit()


# 5. 将基础信息合并到当前股票列表中
print("5. 合并上市日期信息...")
# 使用 inner merge 确保只保留有上市日期信息的股票
filtered_stocks = pd.merge(filtered_stocks, stock_info_all, on='代码', how='inner')
if filtered_stocks.empty:
    print("错误：合并上市日期信息后没有找到匹配的股票数据。")
    #exit()
print(f"   合并后剩余 {len(filtered_stocks)} 只股票。")


# 6. 应用过滤条件
print("6. 应用过滤条件...")

initial_count = len(filtered_stocks)

# 6.1 过滤 科创股/北交股 (代码前缀 688, 8)
# 中小板指数成分股通常不会有科创板或北交所的，但作为通用过滤条件加上
star_bse_filter = filtered_stocks['代码'].astype(str).str.startswith('688') | \
                  filtered_stocks['代码'].astype(str).str.startswith('8')
filtered_stocks = filtered_stocks[~star_bse_filter].copy() # 使用copy避免SettingWithCopyWarning
print(f"   - 过滤 科创股/北交股 后剩余 {len(filtered_stocks)} 只 ({initial_count - len(filtered_stocks)} 只被过滤)")
initial_count = len(filtered_stocks) # Update for next step

# 6.2 过滤 ST风险股 (名称包含 ST 或 *) - 使用原始字符串 r'ST|\*' 避免警告
st_filter = filtered_stocks['名称'].str.contains(r'ST|\*', na=False, regex=True)
filtered_stocks = filtered_stocks[~st_filter].copy() # 使用copy避免SettingWithCopyWarning
print(f"   - 过滤 ST风险股 后剩余 {len(filtered_stocks)} 只 ({initial_count - len(filtered_stocks)} 只被过滤)")
initial_count = len(filtered_stocks)

# 6.3 过滤 停牌股 (成交量为0近似判断)
# 注意：在非交易时间运行，成交量可能都是0，这个过滤会误杀，请在交易时间运行
suspended_filter = filtered_stocks['成交量'] <= 0
filtered_stocks = filtered_stocks[~suspended_filter].copy() # 使用copy避免SettingWithCopyWarning
print(f"   - 过滤 停牌股 (按成交量<=0) 后剩余 {len(filtered_stocks)} 只 ({initial_count - len(filtered_stocks)} 只被过滤)")
initial_count = len(filtered_stocks)

# 6.4 过滤 当日涨停或跌停股票 (通过涨跌额和昨收计算涨跌幅)
# 计算涨跌幅百分比，避免除以0
# 如果昨收为0，则认为无法计算涨跌幅，不进行此过滤（这种情况理论上不存在于正常交易股）
# 或者更保险，直接认为昨收<=0的不能计算涨跌幅，不满足涨跌停条件
calculated_percentage_change = (filtered_stocks['涨跌额'] / filtered_stocks['昨收']) * 100
# 将无穷大（昨收为0导致）和NaN转换为 False，确保不满足涨跌停条件
calculated_percentage_change = calculated_percentage_change.replace([float('inf'), float('-inf')], pd.NA) # Replace inf with NA
calculated_percentage_change = pd.to_numeric(calculated_percentage_change, errors='coerce') # Convert to numeric, errors=coerce will turn NA to NaN

limit_filter = (calculated_percentage_change.abs() >= DAILY_LIMIT_THRESHOLD) # Check absolute value >= threshold
# Note: .abs() on a Series with NaN results in NaN. Comparison like NaN >= threshold is False.
# This correctly handles cases where yesterday's close is 0 or calculation fails.

filtered_stocks = filtered_stocks[~limit_filter].copy() # 使用copy避免SettingWithCopyWarning
print(f"   - 过滤 涨跌停股 (按涨跌额/昨收计算涨跌幅) 后剩余 {len(filtered_stocks)} 只 ({initial_count - len(filtered_stocks)} 只被过滤)")
initial_count = len(filtered_stocks)

# 6.5 过滤 次新股 (上市日期不足指定天数)
today = date.today()
new_stock_threshold_date = today - timedelta(days=NEW_STOCK_DAYS_THRESHOLD)
# Ensure '上市日期' is date only for comparison
new_stock_filter = (filtered_stocks['上市日期'].dt.date > new_stock_threshold_date)
filtered_stocks = filtered_stocks[~new_stock_filter].copy() # 使用copy避免SettingWithCopyWarning
print(f"   - 过滤 次新股 (上市不足 {NEW_STOCK_DAYS_THRESHOLD} 天) 后剩余 {len(filtered_stocks)} 只 ({initial_count - len(filtered_stocks)} 只被过滤)")
initial_count = len(filtered_stocks)

print(f"   总计 {len(filtered_stocks)} 只股票通过所有过滤条件。")

# 7. 按市值升序排序并选取最小的N只
if len(filtered_stocks) < NUM_STOCKS_TO_SELECT:
    print(f"\n警告：通过过滤条件的股票数量 ({len(filtered_stocks)} 只) 少于要求的候选数量 ({NUM_STOCKS_TO_SELECT} 只)。")
    selected_stocks = filtered_stocks.sort_values(by='总市值', ascending=True)
else:
    print(f"\n7. 按总市值升序排序，选取市值最小的 {NUM_STOCKS_TO_SELECT} 只（候选数量，持仓数为 {POSITION_COUNT} 只）。")
    selected_stocks = filtered_stocks.sort_values(by='总市值', ascending=True).head(NUM_STOCKS_TO_SELECT)

# 8. 保存选出的股票到文件
print("\n--- 保存选出的股票到文件 ---")
if selected_stocks.empty:
    print("没有找到符合条件的股票。")
else:
    result_columns = ['代码', '名称']
    selected_stocks[result_columns].to_csv("selected_stocks.csv", index=False, encoding="utf_8_sig")
    print(f"   已将选出的 {len(selected_stocks)} 只股票保存到 selected_stocks.csv 文件。")

print("\n--- 策略结束 ---")
