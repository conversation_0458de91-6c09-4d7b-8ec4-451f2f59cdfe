# 股票技术/市场打分系统

## 系统概述
本系统是一个基于技术指标和市场表现的股票评分系统，主要用于筛选和评估股票的技术面表现。系统通过多个维度的指标对股票进行综合评分，帮助投资者快速识别具有良好技术特征的股票。系统同时提供信号生成功能，包括止损止盈信号、成交量异常信号和特殊时期信号。

## 主要功能
1. **股票评分和排名** - 基于多维度技术指标的综合评分
2. **信号生成系统**
   - 止损止盈信号
   - 成交量异常信号
   - 特殊时期信号
3. **调仓建议生成** - 智能调仓建议（每周二）
4. **技术指标分析** - 涨停次数、相对强度、振幅等
5. **持仓管理** - 完整的持仓管理和编辑功能
6. **交易记录** - 支持分页的交易历史记录
7. **策略调优工具** - 数据驱动的策略优化分析

## 快速开始

### 环境要求
- Python 3.7+
- 网络连接（用于获取股票数据）

### 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd project_0
   ```

2. **安装依赖包**
   ```bash
   pip install -r requirements.txt
   ```

   如果没有requirements.txt文件，手动安装：
   ```bash
   pip install Flask pandas numpy akshare requests urllib3
   ```

3. **启动系统**
   ```bash
   python app.py
   ```

4. **访问系统**
   - 打开浏览器访问：`http://localhost:5000`
   - 系统会自动加载默认股票列表或上传自定义CSV文件

## 功能详解

### 📊 股票评分系统
- **多维度评分**：基于8个核心技术指标
- **智能排名**：自动排序并显示前20名
- **实时更新**：支持手动刷新和定时更新

### 💼 持仓管理系统
- **持仓编辑**：支持修改成本价、数量、备注
- **执行操作**：一键买入/卖出功能
- **盈亏统计**：实时计算已实现/未实现盈亏
- **总盈亏比例**：准确的收益率计算

### 📈 调仓建议系统
- **智能分析**：每周二自动生成调仓建议
- **多种策略**：
  - 跌出候选名单 → 建议卖出
  - 跌出前10名 → 建议卖出
  - 跌出前6名 → 发出预警
  - 亏损超过10% → 止损建议
  - 前6名未持有 → 建议买入

### 📋 交易记录系统
- **分页显示**：支持10/20/50/100条每页
- **智能分页**：完整的分页导航
- **详细记录**：包含日期、股票、操作、价格、数量、金额

### 🔧 策略调优工具
- **数据分析**：`analyze_current_strategy.py`
- **策略优化**：`scoring_strategy_optimizer.py`
- **权重调整**：基于数据驱动的权重优化建议

## 卖出策略体系

### 1. 调仓卖出策略
- **时机**：每周二定期调仓时
- **触发条件**：
  - 股票跌出候选名单
  - 股票跌出前10名
  - 股票跌出前6名（发出预警）

### 2. 止损止盈策略
- **个股止损**：当前价格低于成本价的88%
- **大盘止损**：大盘跌幅超过6%（开盘价的94%）
- **止盈条件**：当前价格达到成本价的200%
- **执行时间**：每天实时监控

### 3. 异常成交量策略
- **监控周期**：120天
- **触发条件**：当日成交量超过120天最高成交量的90%
- **执行时间**：每天下午14:30检查

### 4. 涨停板策略
- **涨停破板**：立即卖出
- **涨停未破板**：继续持有
- **执行时间**：每天实时监控

### 5. 特殊时期策略
- **空仓月份**：1月和4月
- **执行时间**：每天下午14:50检查
- **触发条件**：进入指定月份时自动清仓

## 使用指南

### 基本操作流程
1. **运行评分策略**
   - 上传股票列表CSV文件或使用默认列表
   - 点击"开始分析"进行评分
   - 查看评分结果和排名

2. **持仓管理**
   - 查看当前持仓状态和盈亏情况
   - 编辑持仓信息（成本价、数量等）
   - 执行买入/卖出操作

3. **调仓建议**
   - 每周二查看调仓建议
   - 根据建议执行相应操作
   - 跟踪建议执行效果

4. **交易记录**
   - 查看历史交易记录
   - 使用分页功能浏览大量数据
   - 分析交易模式和效果

### 策略调优
1. **运行分析工具**
   ```bash
   python analyze_current_strategy.py
   ```

2. **查看优化建议**
   ```bash
   python scoring_strategy_optimizer.py
   ```

3. **应用新配置**
   - 根据分析结果调整config.py中的权重
   - 测试新配置的效果
   - 定期回顾和优化

## 系统配置说明

### 基础策略参数
- `POSITION_COUNT = 6`：持仓数量
- `REBALANCE_DAY = 2`：每周调仓日（2代表周二）
- `NEW_STOCK_DAYS_THRESHOLD = 540`：次新股判断阈值（天）
- `INDEX_CODE = "399101"`：基准指数代码（中小板指数）

### 技术指标参数
- `HISTORY_DAYS_FOR_COUNT = 365`：统计历史数据的天数
- `POSITIVE_MOVE_THRESHOLD = 5.0`：大幅上涨阈值
- `RECENT_DAYS_FOR_MOMENTUM = 30`：动量计算天数
- `SHORT_TERM_VOLUME_DAYS = 10`：短期成交量统计天数
- `RECENT_DAYS_FOR_VOLUME = 30`：近期成交额计算天数
- `RELATIVE_STRENGTH_DAYS = 30`：相对强度计算天数
- `DAILY_LIMIT_THRESHOLD = 9.9`：涨跌停判断阈值

### 信号系统参数
1. 止损止盈信号：
   - 支持三种止损策略：个股止损、大盘止损、联合止损
   - 个股止损阈值：88%（相对成本价）
   - 大盘止损阈值：94%（相对开盘价）
   - 止盈阈值：200%（相对成本价）

2. 成交量异常信号：
   - 参考周期：120天
   - 异常阈值：90%（相对历史最高值）

3. 特殊时期信号：
   - 支持按月份设置空仓信号
   - 默认空仓月份：1月和4月

### 评分权重配置
当前系统采用以下权重分配：
- 年涨停次数：25%
- 大幅上涨次数（>5%）：15%
- 30日涨跌幅：15%
- 10日成交强度：15%
- 30日相对强度：10%
- 平均振幅：10%
- 显著振幅天数：5%
- 最大振幅：5%

### 跌停惩罚机制
系统对跌停事件采用时间衰减的惩罚机制：
- **0-3个月内的跌停**：100%权重
- **3-6个月内的跌停**：70%权重
- **6-12个月内的跌停**：40%权重
- **总体惩罚权重**：5%

### 展示配置选项
系统提供以下展示选项：
- 详细评分信息展示
- 成交量分析展示
- 技术指标展示
- 默认展示前20名股票排名

## 文件结构
```
project_0/
├── app.py                          # 主应用程序
├── config.py                       # 配置文件
├── requirements.txt                 # 依赖包列表
├── analyze_current_strategy.py     # 策略分析工具
├── scoring_strategy_optimizer.py   # 策略优化工具
├── test_*.py                       # 测试脚本
├── templates/                      # HTML模板
│   ├── index.html                  # 主页面
│   ├── results.html                # 结果页面
│   └── positions.html              # 持仓管理页面
├── static/                         # 静态文件
└── data/                          # 数据文件
    ├── positions.json              # 持仓数据
    ├── transactions.json           # 交易记录
    └── selected_stocks.csv         # 默认股票列表
```

## 最新更新

### v2.1.0 (2025-06-26)
- ✅ **修复持仓编辑功能**：解决了编辑操作无效的问题
- ✅ **交易记录分页**：添加了完整的分页功能，支持自定义每页显示数量
- ✅ **买入功能完善**：实现了真正的买入逻辑，支持新建持仓和加仓
- ✅ **总盈亏计算修正**：修复了总盈亏比例计算逻辑
- ✅ **策略调优工具**：新增数据驱动的策略分析和优化工具

### v2.0.0 (2025-06-25)
- ✅ **持仓管理系统**：完整的持仓管理功能
- ✅ **调仓建议系统**：智能调仓建议生成
- ✅ **交易记录系统**：完整的交易历史记录
- ✅ **信号生成系统**：多种卖出信号策略

## 故障排除

### 常见问题

1. **依赖包安装失败**
   ```bash
   # 使用国内镜像源
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ -r requirements.txt
   ```

2. **数据获取失败**
   - 检查网络连接
   - 确认akshare版本是否最新
   - 重试几次（数据源可能临时不可用）

3. **持仓编辑无效**
   - 确保已启动应用程序
   - 检查浏览器控制台是否有JavaScript错误
   - 清除浏览器缓存后重试

4. **调仓建议不显示**
   - 确认今天是否为周二（调仓日）
   - 确认已运行过评分策略
   - 检查session数据是否存在

### 性能优化建议

1. **数据缓存**：系统会缓存股票数据，避免频繁请求
2. **分页加载**：交易记录支持分页，提高加载速度
3. **异步处理**：大量股票分析时建议分批处理

## 注意事项
1. 系统默认使用中小板指（399101）作为基准指数
2. 调仓日默认为每周二
3. 持仓数量固定为6只股票
4. 系统会自动过滤上市不足540天的次新股
5. 所有信号和建议仅供参考，投资决策需结合其他因素
6. 特殊时期（1月和4月）会自动生成空仓信号
7. 卖出信号优先级：特殊时期 > 止损信号 > 异常成交量 > 涨停破板 > 调仓信号

## 免责声明
本系统仅供学习和研究使用，所有分析结果和投资建议仅供参考，不构成投资建议。投资有风险，入市需谨慎。使用本系统进行投资决策的风险由用户自行承担。

## 技术支持
如有问题或建议，请通过以下方式联系：
- 提交Issue到项目仓库
- 查看项目文档和代码注释
- 运行测试脚本进行故障诊断