#!/usr/bin/env python3
"""
股票打分系统安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def install_with_mirror(package):
    """使用国内镜像安装包"""
    mirrors = [
        "https://pypi.tuna.tsinghua.edu.cn/simple/",
        "https://mirrors.aliyun.com/pypi/simple/",
        "https://pypi.douban.com/simple/"
    ]
    
    for mirror in mirrors:
        try:
            print(f"尝试使用镜像 {mirror} 安装 {package}")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "-i", mirror, package
            ])
            return True
        except subprocess.CalledProcessError:
            continue
    return False

def main():
    """主安装函数"""
    print("股票打分系统安装程序")
    print("=" * 50)
    
    # 必需的包
    required_packages = [
        "Flask==2.3.3",
        "pandas==2.0.3", 
        "numpy==1.24.3",
        "akshare==1.11.80",
        "requests==2.31.0",
        "urllib3==2.0.4"
    ]
    
    # 可选的包
    optional_packages = [
        "matplotlib>=3.5.0",
        "seaborn>=0.11.0"
    ]
    
    print("正在安装必需的依赖包...")
    failed_packages = []
    
    for package in required_packages:
        print(f"安装 {package}...")
        if not install_package(package):
            print(f"使用默认源安装 {package} 失败，尝试使用国内镜像...")
            if not install_with_mirror(package):
                print(f"❌ 安装 {package} 失败")
                failed_packages.append(package)
            else:
                print(f"✅ 成功安装 {package}")
        else:
            print(f"✅ 成功安装 {package}")
    
    print("\n正在安装可选的依赖包...")
    for package in optional_packages:
        print(f"安装 {package}...")
        if install_package(package):
            print(f"✅ 成功安装 {package}")
        else:
            print(f"⚠️ 安装 {package} 失败（可选包，不影响主要功能）")
    
    print("\n" + "=" * 50)
    if failed_packages:
        print("❌ 安装完成，但以下包安装失败：")
        for package in failed_packages:
            print(f"  - {package}")
        print("\n请手动安装失败的包：")
        for package in failed_packages:
            print(f"  pip install {package}")
    else:
        print("✅ 所有依赖包安装成功！")
    
    print("\n下一步：")
    print("1. 运行系统：python app.py")
    print("2. 打开浏览器访问：http://localhost:5000")
    print("3. 开始使用股票打分系统")

if __name__ == "__main__":
    main()
