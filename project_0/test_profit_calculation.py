#!/usr/bin/env python3
"""
测试总盈亏比例计算逻辑的脚本
"""

def test_profit_calculation():
    """测试盈亏计算逻辑"""
    
    # 模拟交易数据
    transactions = [
        # 买入股票A
        {'action': 'buy', 'value': 10000, 'amount': 1000, 'code': 'A'},
        # 买入股票B  
        {'action': 'buy', 'value': 20000, 'amount': 2000, 'code': 'B'},
        # 卖出部分股票A
        {'action': 'sell', 'value': 6000, 'amount': 500, 'code': 'A'},
    ]
    
    # 当前价格
    current_prices = {'A': 12, 'B': 11}  # A涨了20%, B涨了10%
    
    # 计算逻辑
    total_investment = 0
    total_withdrawal = 0
    stock_trades = {}
    
    for trans in transactions:
        code = trans['code']
        if code not in stock_trades:
            stock_trades[code] = {
                'total_cost': 0.0,
                'total_amount': 0,
                'realized_profit': 0.0
            }
        
        trade = stock_trades[code]
        if trans['action'] == 'buy':
            trade['total_cost'] += trans['value']
            trade['total_amount'] += trans['amount']
            total_investment += trans['value']
        else:  # sell
            total_withdrawal += trans['value']
            if trade['total_amount'] > 0:
                cost_per_share = trade['total_cost'] / trade['total_amount']
                sell_cost = cost_per_share * trans['amount']
                profit = trans['value'] - sell_cost
                trade['realized_profit'] += profit
                trade['total_cost'] = cost_per_share * (trade['total_amount'] - trans['amount'])
                trade['total_amount'] -= trans['amount']
    
    # 计算当前市值和成本
    current_value = 0
    current_cost = 0
    for code, trade in stock_trades.items():
        if trade['total_amount'] > 0:
            current_value += current_prices[code] * trade['total_amount']
            current_cost += trade['total_cost']
    
    # 计算盈亏
    realized_profit = sum(trade['realized_profit'] for trade in stock_trades.values())
    unrealized_profit = current_value - current_cost
    total_profit = realized_profit + unrealized_profit
    
    # 计算比例
    total_profit_pct = (total_profit / total_investment) * 100 if total_investment > 0 else 0
    
    print("=== 交易分析 ===")
    print(f"总投资: {total_investment}")
    print(f"总卖出: {total_withdrawal}")
    print(f"已实现盈亏: {realized_profit}")
    print(f"未实现盈亏: {unrealized_profit}")
    print(f"总盈亏: {total_profit}")
    print(f"当前市值: {current_value}")
    print(f"当前成本: {current_cost}")
    print(f"总盈亏比例: {total_profit_pct:.2f}%")
    
    print("\n=== 详细分析 ===")
    for code, trade in stock_trades.items():
        if trade['total_amount'] > 0:
            current_price = current_prices[code]
            market_value = current_price * trade['total_amount']
            cost = trade['total_cost']
            unrealized = market_value - cost
            print(f"股票{code}: 持仓{trade['total_amount']}股, 成本{cost}, 市值{market_value}, 未实现盈亏{unrealized}")
        print(f"股票{code}: 已实现盈亏{trade['realized_profit']}")

if __name__ == '__main__':
    test_profit_calculation()
