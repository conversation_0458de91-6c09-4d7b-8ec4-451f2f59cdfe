#!/usr/bin/env python3
"""
测试修复后的总盈亏比例计算
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import INITIAL_CAPITAL

def test_fixed_calculation():
    """测试修复后的计算逻辑"""
    
    print("=== 测试修复后的总盈亏比例计算 ===\n")
    
    # 模拟您的实际数据（从之前的调试信息）
    realized_profit = 7889.00    # 已实现盈亏
    unrealized_profit = 1272.00  # 未实现盈亏
    total_investment = 190409.00 # 历史总投资（错误的分母）
    
    # 计算总盈亏
    total_profit = realized_profit + unrealized_profit
    
    print(f"初始资金: {INITIAL_CAPITAL:,.2f}")
    print(f"已实现盈亏: {realized_profit:,.2f}")
    print(f"未实现盈亏: {unrealized_profit:,.2f}")
    print(f"总盈亏: {total_profit:,.2f}")
    print()
    
    # 错误的计算方法（之前的bug）
    if total_investment > 0:
        wrong_profit_pct = (total_profit / total_investment) * 100
    else:
        wrong_profit_pct = 0.0
    
    # 正确的计算方法（修复后）
    if INITIAL_CAPITAL > 0:
        correct_profit_pct = (total_profit / INITIAL_CAPITAL) * 100
    else:
        correct_profit_pct = 0.0
    
    print("=== 计算结果对比 ===")
    print(f"错误方法（使用历史总投资）:")
    print(f"  分母: {total_investment:,.2f}")
    print(f"  计算: {total_profit:,.2f} / {total_investment:,.2f} * 100")
    print(f"  结果: {wrong_profit_pct:.2f}%")
    print()
    
    print(f"正确方法（使用初始资金）:")
    print(f"  分母: {INITIAL_CAPITAL:,.2f}")
    print(f"  计算: {total_profit:,.2f} / {INITIAL_CAPITAL:,.2f} * 100")
    print(f"  结果: {correct_profit_pct:.2f}%")
    print()
    
    print("=== 差异分析 ===")
    print(f"差异: {abs(correct_profit_pct - wrong_profit_pct):.2f}个百分点")
    print(f"正确结果是错误结果的 {correct_profit_pct / wrong_profit_pct:.1f} 倍")
    print()
    
    print("=== 为什么之前的计算是错误的 ===")
    print("1. 历史总投资会累积所有买入金额，包括卖出后再买入的资金")
    print("2. 这导致分母虚高，收益率被人为压低")
    print("3. 正确的做法是用初始资金作为基准，反映真实的投资收益率")
    print()
    
    print("=== 验证逻辑 ===")
    print("假设您用6万初始资金:")
    print("- 买入3万股票A，卖出获得3.5万（盈利5000）")
    print("- 再买入4万股票B，现在浮盈3000")
    print("- 总盈亏 = 5000 + 3000 = 8000")
    print("- 正确收益率 = 8000 / 60000 = 13.33%")
    print("- 错误方法会用 (3万+4万) = 7万作为分母，得到 11.43%")
    print("- 但实际上您只投入了6万，不是7万！")

if __name__ == "__main__":
    test_fixed_calculation()
